using System;
using System.IO;
using System.Text;
using Microsoft.Extensions.Logging;
using Hl7MessageEnhancer.Services;

class DebugComparison
{
    static void Main()
    {
        try
        {
            Console.WriteLine("=== Debug Comparison for HL7 Processing ===");
            
            // Setup logging
            using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));
            var logger = loggerFactory.CreateLogger<Hl7Processor>();
            
            // File paths
            var sampleFile = @"C:\Users\<USER>\RiderProjects\Hl7MessageEnhancer\TestDebugOutput\rawhl7messages\sample-hl7.hl7";
            var expectedFile = @"C:\Users\<USER>\RiderProjects\Hl7MessageEnhancer\TestOutput2\expected-hl7.hl7";
            var tempSourceDir = Path.Combine(Path.GetTempPath(), "hl7_debug_source");
            var tempOutputDir = Path.Combine(Path.GetTempPath(), "hl7_debug_output");
            
            // Clean and create temp directories
            if (Directory.Exists(tempSourceDir)) Directory.Delete(tempSourceDir, true);
            if (Directory.Exists(tempOutputDir)) Directory.Delete(tempOutputDir, true);
            Directory.CreateDirectory(tempSourceDir);
            Directory.CreateDirectory(tempOutputDir);
            
            // Copy sample file to temp source
            var tempSamplePath = Path.Combine(tempSourceDir, "sample-hl7.hl7");
            File.Copy(sampleFile, tempSamplePath);
            
            Console.WriteLine($"Processing file: {tempSamplePath}");
            
            // Process the file
            var processor = new Hl7Processor(logger, tempSourceDir, tempOutputDir, true);
            processor.Run();
            
            // Read processed output
            var processedFile = Path.Combine(tempOutputDir, "sample-hl7.hl7");
            if (File.Exists(processedFile))
            {
                var processedContent = File.ReadAllText(processedFile, Encoding.UTF8);
                var expectedContent = File.ReadAllText(expectedFile, Encoding.UTF8);
                
                Console.WriteLine("\n=== PROCESSED OUTPUT ===");
                Console.WriteLine(processedContent);
                
                Console.WriteLine("\n=== EXPECTED OUTPUT ===");
                Console.WriteLine(expectedContent);
                
                // Split and compare line by line
                var processedLines = SplitHL7Content(processedContent);
                var expectedLines = SplitHL7Content(expectedContent);
                
                Console.WriteLine($"\n=== LINE COMPARISON ===");
                Console.WriteLine($"Processed lines: {processedLines.Count}");
                Console.WriteLine($"Expected lines: {expectedLines.Count}");
                
                for (int i = 0; i < Math.Max(processedLines.Count, expectedLines.Count); i++)
                {
                    var processedLine = i < processedLines.Count ? processedLines[i] : "[MISSING]";
                    var expectedLine = i < expectedLines.Count ? expectedLines[i] : "[MISSING]";
                    
                    if (processedLine != expectedLine)
                    {
                        Console.WriteLine($"\nDIFFERENCE at line {i + 1}:");
                        Console.WriteLine($"Processed: {processedLine}");
                        Console.WriteLine($"Expected:  {expectedLine}");
                        
                        // Show character-by-character difference
                        var minLength = Math.Min(processedLine.Length, expectedLine.Length);
                        for (int j = 0; j < minLength; j++)
                        {
                            if (processedLine[j] != expectedLine[j])
                            {
                                Console.WriteLine($"First difference at position {j}: '{processedLine[j]}' vs '{expectedLine[j]}'");
                                break;
                            }
                        }
                        if (processedLine.Length != expectedLine.Length)
                        {
                            Console.WriteLine($"Length difference: Processed={processedLine.Length}, Expected={expectedLine.Length}");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"Line {i + 1}: MATCH");
                    }
                }
            }
            else
            {
                Console.WriteLine("ERROR: Processed file not found!");
            }
            
            // Clean up
            if (Directory.Exists(tempSourceDir)) Directory.Delete(tempSourceDir, true);
            if (Directory.Exists(tempOutputDir)) Directory.Delete(tempOutputDir, true);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }
    
    private static List<string> SplitHL7Content(string content)
    {
        var normalized = content.Replace("\r\n", "\r").Replace("\n", "\r");
        return normalized.Split('\r', StringSplitOptions.RemoveEmptyEntries).ToList();
    }
}