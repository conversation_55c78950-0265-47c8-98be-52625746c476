using Microsoft.Extensions.Logging;
using Hl7MessageEnhancer.Services;
using System;
using System.IO;
using System.Linq;

class TestComparison
{
    static void Main()
    {
        var logger = LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<HL7MessageProcessor>();
        var processor = new HL7MessageProcessor(logger, "DEFAULT");
        
        // Read the input file
        var inputPath = @"c:\Users\<USER>\RiderProjects\Hl7MessageEnhancer\TestMessageEnhancer\rawhl7messages\sample-hl7.hl7";
        var expectedPath = @"c:\Users\<USER>\RiderProjects\Hl7MessageEnhancer\TestMessageEnhancer\expected-hl7.hl7";
        
        var inputContent = File.ReadAllText(inputPath);
        var expectedContent = File.ReadAllText(expectedPath);
        
        Console.WriteLine("=== INPUT ====");
        Console.WriteLine(inputContent);
        
        var processed = processor.ProcessHL7Message(inputContent);
        
        Console.WriteLine("\n=== PROCESSED ====");
        Console.WriteLine(processed);
        
        Console.WriteLine("\n=== EXPECTED ====");
        Console.WriteLine(expectedContent);
        
        // Compare line by line
        var processedLines = processed.Split('\n').Select(l => l.Trim()).ToArray();
        var expectedLines = expectedContent.Split('\n').Select(l => l.Trim()).ToArray();
        
        Console.WriteLine("\n=== COMPARISON ====");
        Console.WriteLine($"Processed lines: {processedLines.Length}");
        Console.WriteLine($"Expected lines: {expectedLines.Length}");
        
        for (int i = 0; i < Math.Max(processedLines.Length, expectedLines.Length); i++)
        {
            var processedLine = i < processedLines.Length ? processedLines[i] : "[MISSING]";
            var expectedLine = i < expectedLines.Length ? expectedLines[i] : "[MISSING]";
            
            if (processedLine != expectedLine)
            {
                Console.WriteLine($"\nDIFFERENCE at line {i + 1}:");
                Console.WriteLine($"PROCESSED: {processedLine}");
                Console.WriteLine($"EXPECTED:  {expectedLine}");
            }
        }
    }
}