using Microsoft.Extensions.Logging;
using Hl7MessageEnhancer.Services;
using System.Text;

class DebugEnhancement
{
    static void Main()
    {
        // Create logger
        using var loggerFactory = LoggerFactory.Create(builder => 
            builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        var logger = loggerFactory.CreateLogger<HL7MessageProcessor>();
        
        // Read sample HL7 content
        var samplePath = @"c:\Users\<USER>\RiderProjects\Hl7MessageEnhancer\TestDebugOutput\sample-hl7.hl7";
        var expectedPath = @"c:\Users\<USER>\RiderProjects\Hl7MessageEnhancer\TestDebugOutput\expected-hl7.hl7";
        
        if (!File.Exists(samplePath))
        {
            Console.WriteLine($"Sample file not found: {samplePath}");
            return;
        }
        
        var sampleContent = File.ReadAllText(samplePath, Encoding.UTF8);
        Console.WriteLine("=== ORIGINAL SAMPLE ====");
        Console.WriteLine(sampleContent);
        Console.WriteLine();
        
        // Process with HL7MessageProcessor
        var processor = new HL7MessageProcessor(logger, "DEFAULT");
        var processedContent = processor.ProcessHL7Message(sampleContent);
        
        Console.WriteLine("=== PROCESSED OUTPUT ====");
        Console.WriteLine(processedContent);
        Console.WriteLine();
        
        // Read expected content if it exists
        if (File.Exists(expectedPath))
        {
            var expectedContent = File.ReadAllText(expectedPath, Encoding.UTF8);
            Console.WriteLine("=== EXPECTED OUTPUT ====");
            Console.WriteLine(expectedContent);
            Console.WriteLine();
            
            // Compare line by line
            var processedLines = processedContent.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
            var expectedLines = expectedContent.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
            
            Console.WriteLine("=== LINE BY LINE COMPARISON ====");
            Console.WriteLine($"Processed lines: {processedLines.Length}");
            Console.WriteLine($"Expected lines: {expectedLines.Length}");
            Console.WriteLine();
            
            var maxLines = Math.Max(processedLines.Length, expectedLines.Length);
            for (int i = 0; i < maxLines; i++)
            {
                var procLine = i < processedLines.Length ? processedLines[i] : "<MISSING>";
                var expLine = i < expectedLines.Length ? expectedLines[i] : "<MISSING>";
                
                if (procLine != expLine)
                {
                    Console.WriteLine($"DIFF Line {i + 1}:");
                    Console.WriteLine($"  Processed: {procLine}");
                    Console.WriteLine($"  Expected:  {expLine}");
                    Console.WriteLine();
                }
                else
                {
                    Console.WriteLine($"OK   Line {i + 1}: {procLine}");
                }
            }
        }
        else
        {
            Console.WriteLine($"Expected file not found: {expectedPath}");
        }
    }
}