using Microsoft.Extensions.Logging;
using Hl7MessageEnhancer.Services;
using System;

class DebugOBXMapping
{
    static void Main()
    {
        var logger = LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger<HL7MessageProcessor>();
        var processor = new HL7MessageProcessor(logger, "DEFAULT");
        
        var sampleHL7 = @"MSH|^~\&|RHAPSODY|PHCC|RHAPSODY|PHCC|20241019143000||ADT^A08^ADT_A01|20241019143000|P|2.5
EVN||20241019143000||||
PID|1||HC02287391^^^MRN^MR^RHAPSODY_CON_SYS~***********^^^""MOI""^""SSN""^^^20240212~HC02287391^^^MRN^MRN^~625664-I^03050476^^0^0^^^^^PHCC Historical MRN^Passport^^Historical MRN^Passport^^medicom^^||ABED^AHMED^^^^^official~ÚÈíÏ^ÇÍãÏ^^^^^usual~^ÇÍãÏ^^^^^usual||19930804000000|male|ÚÈíÏ^ÇÍãÏ^^^^^usual|Non National|AL SHAHANIYA^^^^^Qatar^Home^^Al Sheehaniya~^^^^^^Home^^~<EMAIL>^^^^^^E-mail^^||+***********^mobile^~+***********^mobile^Tel|55008227^Business^Tel|Arabic|Single||*********^^^NH^FIN NBR^|||||||0|||Yemeni||No
PD1||||||||||||||||||||||||||||
PV1|1|I|ICU^101^01^PHCC|R|||10096519^Wally^Ahmed^Nourelfalah Mahmoud||||ICU||||R||10096519^Wally^Ahmed^Nourelfalah Mahmoud|IP|NH|||||||||||||||||||||20241019143000||||||
OBX|1|DT|FULLREG||20231019||||||
OBX|2|DT|HC EXP DATE||20240405||||||
OBX|3|DT|QATAR_ID_EXP||20240212||||||
OBX|4|TX|PRIM_ORG_NAME||Rawdat Al Khail Health Center||||||
OBX|5|CD|FAMILY_PHYSICIAN||10096519^Wally^Ahmed^Nourelfalah Mahmoud||||||";
        
        Console.WriteLine("=== ORIGINAL ====");
        Console.WriteLine(sampleHL7);
        
        var processed = processor.ProcessHL7Message(sampleHL7);
        
        Console.WriteLine("\n=== PROCESSED ====");
        Console.WriteLine(processed);
        
        // Check if HC EXP DATE was mapped
        var pidLine = processed.Split('\n').FirstOrDefault(l => l.StartsWith("PID"));
        if (pidLine != null)
        {
            Console.WriteLine("\n=== PID LINE ====");
            Console.WriteLine(pidLine);
            
            if (pidLine.Contains("20240405"))
            {
                Console.WriteLine("✓ HC EXP DATE (20240405) found in PID segment");
            }
            else
            {
                Console.WriteLine("✗ HC EXP DATE (20240405) NOT found in PID segment");
            }
        }
    }
}