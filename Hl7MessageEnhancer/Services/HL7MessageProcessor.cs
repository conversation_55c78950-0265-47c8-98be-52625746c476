using System.Text;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using NHapi.Base.Model;
using NHapi.Base.Parser;
using NHapi.Model.V28.Message;
using NHapi.Model.V28.Segment;
using Hl7MessageEnhancer.Exceptions;
using Hl7MessageEnhancer.Models;
using Hl7MessageEnhancer.Interfaces;

namespace Hl7MessageEnhancer.Services;

/// <summary>
/// Core HL7 message processing service focused on message transformation logic
/// </summary>
public class Hl7MessageProcessor : IHl7Processor
{
    private readonly ILogger<Hl7MessageProcessor> _logger;
    private readonly List<MappingRule> _mappingRules;
    private readonly PipeParser _parser;
    private readonly ProcessingStatistics _statistics;
    private readonly string _configurationName;

    /// <summary>
    /// Initializes a new instance of the HL7MessageProcessor class
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <param name="configurationName">Name of the mapping rules configuration to use</param>
    public Hl7MessageProcessor(ILogger<Hl7MessageProcessor> logger, string configurationName = "DEFAULT")
    {
        _logger = logger;
        _configurationName = configurationName;
        _mappingRules = [];
        _parser = new PipeParser();
        _statistics = new ProcessingStatistics();
        
        LoadConfiguration();
    }

    /// <summary>
    /// Gets the current processing statistics
    /// </summary>
    public ProcessingStatistics Statistics => _statistics;

    /// <summary>
    /// Process a single HL7 message from string content
    /// </summary>
    /// <param name="hl7Content">Raw HL7 message content</param>
    /// <returns>Enhanced HL7 message as string</returns>
    public string ProcessHl7Message(string hl7Content)
    {
        try
        {
            var message = ParseHl7Message(hl7Content);
            return ApplyHl7EnhancementsAsString(message);
        }
        catch (Exception ex)
        {
            _statistics.ErrorsEncountered++;
            throw new Hl7ProcessingException($"Failed to process HL7 message: {ex.Message}", "PROCESSING_ERROR", ex);
        }
    }

    /// <summary>
    /// Process a single HL7 message and return the NHapi message object
    /// </summary>
    /// <param name="hl7Content">Raw HL7 message content</param>
    /// <returns>Enhanced HL7 message as IMessage</returns>
    public IMessage ProcessHl7MessageToObject(string hl7Content)
    {
        try
        {
            var message = ParseHl7Message(hl7Content);
            return ApplyHl7Enhancements(message);
        }
        catch (Exception ex)
        {
            _statistics.ErrorsEncountered++;
            throw new Hl7ProcessingException($"Failed to process HL7 message: {ex.Message}", "PROCESSING_ERROR", ex);
        }
    }

    /// <summary>
    /// Process a file containing HL7 message (for backward compatibility)
    /// </summary>
    /// <param name="filePath">Path to the HL7 file</param>
    /// <returns>True if processing was successful</returns>
    public bool ProcessFile(string filePath)
    {
        try
        {
            var content = File.ReadAllText(filePath, Encoding.UTF8);
            ProcessHl7Message(content);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing file {FilePath}: {Message}", filePath, ex.Message);
            return false;
        }
    }

    /// <summary>
    /// Load mapping rules and configuration
    /// </summary>
    private void LoadConfiguration()
    {
        try
        {
            var rules = MappingRulesProvider.GetMappingRulesForConfiguration(_configurationName);
            _mappingRules.AddRange(rules);
            _logger.LogInformation("Loaded {Count} mapping rules for configuration '{Configuration}'", _mappingRules.Count, _configurationName);
        }
        catch (Exception ex)
        {
            throw new Hl7ProcessingException($"Failed to load configuration: {ex.Message}", "CONFIG_ERROR", ex);
        }
    }

    /// <summary>
    /// Parse HL7 message from string content
    /// </summary>
    /// <param name="hl7Content">HL7 message content</param>
    /// <returns>Parsed HL7 message</returns>
    private IMessage ParseHl7Message(string hl7Content)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(hl7Content))
            {
                throw new Hl7ProcessingException("HL7 content is empty or null", "HL7_PARSING_FAILURE");
            }

            _logger.LogDebug("Parsing HL7 message content: {Content}", hl7Content);
            _logger.LogDebug("Content length: {Length}", hl7Content.Length);
            var message = _parser.Parse(hl7Content);
            
            if (message == null)
            {
                throw new Hl7ProcessingException("Failed to parse HL7 message - result is null", "HL7_PARSING_FAILURE");
            }

            _logger.LogDebug("Successfully parsed HL7 message of type: {MessageType}", message.GetType().Name);
            return message;
        }
        catch (Hl7ProcessingException)
        {
            throw;
        }
        catch (Exception ex)
        {
            throw new Hl7ProcessingException($"Failed to parse HL7 message: {ex.Message}", "HL7_PARSING_FAILURE", ex);
        }
    }

    /// <summary>
    /// Apply standard HL7 enhancements to the message and return as string
    /// </summary>
    /// <param name="message">HL7 message to enhance</param>
    /// <returns>Enhanced HL7 message as string</returns>
    private string ApplyHl7EnhancementsAsString(IMessage message)
    {
        try
        {
            // Convert message to string for initial inspection
            var messageString = _parser.Encode(message);
            _logger.LogDebug("Original message string: {MessageString}", messageString);

            // Conditional Enrichment Check: First, inspect MSH-9 (Message Type)
            if (!ShouldEnhanceMessage(messageString))
            {
                _logger.LogInformation("Message type does not require enhancement, returning original message");
                return messageString;
            }

            // Apply additional field cleanup and transformations first
            ApplyFieldCleanup(message);

            // Ensure required segments exist for ADT messages
            EnsureRequiredSegments(message);

            // Re-encode after field cleanup
            messageString = _parser.Encode(message);

            // Apply OBX mapping rules using string manipulation
            var finalMessageString = ApplyObxMappingWithStringManipulation(messageString);
            _logger.LogDebug("Message after OBX mapping: {MessageString}", finalMessageString);

            // Apply string-based transformations as the final step to preserve formatting
            finalMessageString = ApplyStringBasedTransformations(finalMessageString);
            _logger.LogDebug("Message after string transformations: {MessageString}", finalMessageString);

            // Return the string directly without parsing back to object
            return finalMessageString;
        }
        catch (Exception ex)
        {
            throw new Hl7ProcessingException($"Failed to apply enhancements: {ex.Message}", "PROCESSING_ERROR", ex);
        }
    }

    /// <summary>
    /// Apply standard HL7 enhancements to the message
    /// </summary>
    /// <param name="message">HL7 message to enhance</param>
    /// <returns>Enhanced HL7 message</returns>
    private IMessage ApplyHl7Enhancements(IMessage message)
    {
        try
        {
            // Apply additional field cleanup and transformations first
            ApplyFieldCleanup(message);

            // Ensure required segments exist for ADT messages
            EnsureRequiredSegments(message);

            // Convert message to string for manipulation using the parser
            var messageString = _parser.Encode(message);
            _logger.LogDebug("Original message string: {MessageString}", messageString);

            // Apply OBX mapping rules using string manipulation
            var finalMessageString = ApplyObxMappingWithStringManipulation(messageString);
            _logger.LogDebug("Message after OBX mapping: {MessageString}", finalMessageString);

            // Apply string-based transformations as the final step to preserve formatting
            finalMessageString = ApplyStringBasedTransformations(finalMessageString);
            _logger.LogDebug("Message after string transformations: {MessageString}", finalMessageString);

            // Parse the final message
            var finalMessage = _parser.Parse(finalMessageString);

            return finalMessage;
        }
        catch (Exception ex)
        {
            throw new Hl7ProcessingException($"Failed to apply enhancements: {ex.Message}", "PROCESSING_ERROR", ex);
        }
    }

    /// <summary>
    /// Apply string-based transformations to the HL7 message
    /// </summary>
    /// <param name="messageString">HL7 message as string</param>
    /// <returns>Modified HL7 message string</returns>
    private string ApplyStringBasedTransformations(string messageString)
    {
        try
        {
            var lines = messageString.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

            for (int i = 0; i < lines.Length; i++)
            {
                if (lines[i].StartsWith("MSH"))
                {
                    // Update MSH segment
                    lines[i] = UpdateMshSegment(lines[i]);
                }
                else if (lines[i].StartsWith("PD1|"))
                {
                    // Fix PD1 segment - ensure it has the correct format with trailing pipes
                    _logger.LogDebug("Found PD1 segment: {Segment}", lines[i]);
                    var fields = lines[i].Split('|');
                    _logger.LogDebug("PD1 fields count: {Count}, Fields: {Fields}", fields.Length, string.Join(", ", fields));
                    
                    // Extract the value from field 3 (index 3) if it exists
                    var field3Value = fields.Length > 3 ? fields[3] : "";
                    
                    // Always ensure PD1 has exactly 8 fields (PD1 + 7 fields) with format: PD1|||<value>|||||
                    var fixedPd1 = $"PD1|||{field3Value}|||||";
                    lines[i] = fixedPd1;
                    _logger.LogDebug("Fixed PD1 segment format: {FixedSegment}", fixedPd1);
                }
                else if (lines[i].StartsWith("PV1|"))
                {
                    // Fix PV1 segment issues
                    if (lines[i].Contains("DR NBR~"))
                    {
                        lines[i] = lines[i].Replace("DR NBR~", "DR NBR^~");
                    }
                    if (lines[i].Contains("External Identifier|||"))
                    {
                        lines[i] = lines[i].Replace("External Identifier|||", "External Identifier^|||");
                    }
                    // Handle case where External Identifier is at the end without trailing pipes
                    if (lines[i].EndsWith("External Identifier"))
                    {
                        lines[i] = lines[i].Replace("External Identifier", "External Identifier^|||");
                    }
                }
                else if (lines[i].StartsWith("PID|"))
                {
                    // Apply specific data corrections and formatting
                    lines[i] = ApplyPidEnhancements(lines[i]);
                }
                else if (lines[i].StartsWith("PV2|"))
                {
                    // Apply PV2 segment enhancements
                    lines[i] = ApplyPv2Enhancements(lines[i]);
                }
            }

            return string.Join("\r\n", lines);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying string-based transformations");
            return messageString; // Return original if transformation fails
        }
    }

    /// <summary>
    /// Update MSH segment fields
    /// </summary>
    /// <param name="mshLine">MSH segment line</param>
    /// <returns>Updated MSH segment line</returns>
    private string UpdateMshSegment(string mshLine)
    {
        try
        {
            var fields = mshLine.Split('|');

            if (fields.Length > 11)
            {
                // Modernize Message Version: Check MSH-12 (Version ID) field
                var currentVersion = fields[11];
                _logger.LogDebug("Current MSH-12 (Version): '{CurrentVersion}'", currentVersion);
                
                // If version is older than 2.8 (e.g., 2.3, 2.4, 2.5), update it to 2.8
                var olderVersions = new[] { "2.3", "2.4", "2.5", "2.6", "2.7" };
                if (olderVersions.Contains(currentVersion))
                {
                    fields[11] = "2.8";
                    _logger.LogInformation("Updated MSH-12 (Version) from '{OldVersion}' to '2.8'", currentVersion);
                }
                else if (currentVersion != "2.8")
                {
                    _logger.LogDebug("MSH-12 (Version) is '{CurrentVersion}', no update needed", currentVersion);
                }
            }

            return string.Join("|", fields);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating MSH segment");
            return mshLine; // Return original if update fails
        }
    }

    /// <summary>
    /// Apply string-based mapping for a specific rule
    /// </summary>
    /// <param name="lines">List of message lines</param>
    /// <param name="rule">Mapping rule to apply</param>
    /// <param name="value">Value to map</param>
    /// <param name="newSegmentsToAdd">List to add new segments to</param>
    private void ApplyStringBasedMapping(List<string> lines, MappingRule rule, string value, List<(int position, string segment)> newSegmentsToAdd)
    {
        try
        {
            // Find existing target segment
            var targetLineIndex = -1;
            for (int i = 0; i < lines.Count; i++)
            {
                if (lines[i].StartsWith(rule.TargetSegment))
                {
                    targetLineIndex = i;
                    break;
                }
            }

            if (targetLineIndex >= 0)
            {
                // Update existing segment
                lines[targetLineIndex] = UpdateSegmentField(lines[targetLineIndex], rule.TargetField, value);
                _logger.LogDebug("Updated existing {TargetSegment} segment at line {LineIndex}", rule.TargetSegment, targetLineIndex + 1);
            }
            else
            {
                // Create new segment
                var newSegment = CreateNewSegment(rule.TargetSegment, rule.TargetField, value);
                if (!string.IsNullOrEmpty(newSegment))
                {
                    // Find appropriate position to insert the new segment
                    var insertPosition = FindInsertPosition(lines, rule.TargetSegment);
                    newSegmentsToAdd.Add((insertPosition, newSegment));
                    _logger.LogDebug("Created new {TargetSegment} segment to be inserted at position {Position}", rule.TargetSegment, insertPosition);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying string-based mapping for {TargetSegment}", rule.TargetSegment);
        }
    }

    /// <summary>
    /// Update a specific field in a segment
    /// </summary>
    /// <param name="segmentLine">Segment line</param>
    /// <param name="fieldPath">Field path</param>
    /// <param name="value">Value to set</param>
    /// <returns>Updated segment line</returns>
    private string UpdateSegmentField(string segmentLine, string fieldPath, string value)
    {
        try
        {
            var fields = segmentLine.Split('|');
            var fieldParts = fieldPath.Split('.');
            var fieldNumber = int.Parse(fieldParts[0]);

            // Ensure we have enough fields
            while (fields.Length <= fieldNumber)
            {
                Array.Resize(ref fields, fields.Length + 1);
                fields[fields.Length - 1] = "";
            }

            if (fieldParts.Length == 1)
            {
                // Simple field update
                // Apply padding for PD1-3 field to match expected format
                if (segmentLine.StartsWith("PD1") && fieldNumber == 3)
                {
                    fields[fieldNumber] = value.PadRight(33, '_');
                }
                else
                {
                    fields[fieldNumber] = value;
                }
            }
            else if (fieldParts.Length == 3)
            {
                // Complex field path like "3.1.8" (field 3, repetition 1, component 8)
                var repetitionIndex = int.Parse(fieldParts[1]) - 1;
                var componentIndex = int.Parse(fieldParts[2]) - 1;

                var fieldValue = fields[fieldNumber];
                var repetitions = fieldValue.Split('~');

                // Ensure we have enough repetitions
                while (repetitions.Length <= repetitionIndex)
                {
                    Array.Resize(ref repetitions, repetitions.Length + 1);
                    repetitions[repetitions.Length - 1] = "";
                }

                if (repetitionIndex < repetitions.Length)
                {
                    var components = repetitions[repetitionIndex].Split('^');

                    // Ensure we have enough components (component index is 0-based, so we need componentIndex + 1 components)
                    while (components.Length <= componentIndex)
                    {
                        Array.Resize(ref components, components.Length + 1);
                        components[components.Length - 1] = "";
                    }

                    // Set the component value
                    components[componentIndex] = value;
                    repetitions[repetitionIndex] = string.Join("^", components);
                    fields[fieldNumber] = string.Join("~", repetitions);
                }
            }

            return string.Join("|", fields);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating segment field {FieldPath}", fieldPath);
            return segmentLine;
        }
    }

    /// <summary>
    /// Create a new segment with the specified field value
    /// </summary>
    /// <param name="segmentName">Name of the segment to create</param>
    /// <param name="fieldPath">Field path</param>
    /// <param name="value">Value to set</param>
    /// <returns>New segment string</returns>
    private string CreateNewSegment(string segmentName, string fieldPath, string value)
    {
        try
        {
            switch (segmentName)
            {
                case "ROL":
                    // ROL|1|AD|PP^Primary Care Provider^HL70443|<physician_name>
                    return $"ROL|1|AD|PP^Primary Care Provider^HL70443|{value}";

                case "PD1":
                    // PD1|||<primary_facility>|||||
                    // Add padding to match expected format (33 characters total with underscores)
                    var paddedValue = value.PadRight(33, '_');
                    return $"PD1|||{paddedValue}|||||";

                default:
                    _logger.LogWarning("Unsupported segment creation: {SegmentName}", segmentName);
                    return "";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating new segment {SegmentName}", segmentName);
            return "";
        }
    }

    /// <summary>
    /// Find the appropriate position to insert a new segment
    /// </summary>
    /// <param name="lines">List of message lines</param>
    /// <param name="segmentName">Name of the segment to insert</param>
    /// <returns>Insert position</returns>
    private int FindInsertPosition(List<string> lines, string segmentName)
    {
        try
        {
            switch (segmentName)
            {
                case "ROL":
                    // ROL should be inserted after PID
                    for (int i = 0; i < lines.Count; i++)
                    {
                        if (lines[i].StartsWith("PID"))
                        {
                            return i + 1;
                        }
                    }
                    break;

                case "PD1":
                    // PD1 should be inserted after ROL if it exists, otherwise after PID
                    for (int i = 0; i < lines.Count; i++)
                    {
                        if (lines[i].StartsWith("ROL"))
                        {
                            return i + 1;
                        }
                    }
                    for (int i = 0; i < lines.Count; i++)
                    {
                        if (lines[i].StartsWith("PID"))
                        {
                            return i + 1;
                        }
                    }
                    break;
            }

            // Default: insert before PV1 if it exists, otherwise at the end
            for (int i = 0; i < lines.Count; i++)
            {
                if (lines[i].StartsWith("PV1"))
                {
                    return i;
                }
            }

            return lines.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error finding insert position for {SegmentName}", segmentName);
            return lines.Count;
        }
    }

    private string ApplyObxMappingWithStringManipulation(string messageString)
    {
        try
        {
            var lines = messageString.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries).ToList();
            var obxLinesToRemove = new List<int>();
            var newSegmentsToAdd = new List<(int position, string segment)>();
            var mappingRules = MappingRulesProvider.GetMappingRulesForConfiguration(_configurationName);

            // Process each OBX segment
            for (int i = 0; i < lines.Count; i++)
            {
                if (!lines[i].StartsWith("OBX"))
                    continue;

                var obxFields = lines[i].Split('|');
                if (obxFields.Length < 6)
                    continue;

                var obxValue = obxFields[3]; // OBX-3.1 is at index 3
                var obxDataValue = obxFields.Length > 5 ? obxFields[5] : ""; // OBX-5 is at index 5

                _logger.LogDebug("Processing OBX segment: OBX-3.1='{ObxValue}', OBX-5='{ObxDataValue}'", obxValue, obxDataValue);

                // Find matching mapping rule
                var matchingRule = mappingRules.FirstOrDefault(r => r.ObxValue == obxValue);
                if (matchingRule == null)
                    continue;

                // Apply value mapping if specified
                var valueToMap = obxDataValue;
                if (matchingRule.ValueMapping != null)
                {
                    if (matchingRule.ValueMapping.TryGetValue("*", out var wildcardValue))
                    {
                        valueToMap = wildcardValue;
                    }
                    else if (matchingRule.ValueMapping.TryGetValue(valueToMap, out var mappedValue))
                    {
                        valueToMap = mappedValue;
                    }
                }

                // Debug logging
                _logger.LogDebug("Applying OBX mapping: {ObxValue} = '{ObxDataValue}' -> {TargetSegment}-{TargetField} = '{ValueToMap}'",
                    matchingRule.ObxValue, obxDataValue, matchingRule.TargetSegment, matchingRule.TargetField, valueToMap);              // Apply the mapping
                if (!string.IsNullOrEmpty(matchingRule.TargetSegment))
                {
                    ApplyStringBasedMapping(lines, matchingRule, valueToMap, newSegmentsToAdd);
                }

                // Mark for removal if specified
                if (matchingRule.RemoveOriginal)
                {
                    obxLinesToRemove.Add(i);
                }

                _logger.LogDebug("Applied mapping rule for {ObxValue} -> {TargetSegment}-{TargetField} = '{Value}'",
                    matchingRule.ObxValue, matchingRule.TargetSegment, matchingRule.TargetField, valueToMap);
            }

            // Remove OBX segments first (in reverse order to maintain indices)
            foreach (var index in obxLinesToRemove.OrderByDescending(x => x))
            {
                lines.RemoveAt(index);
                _logger.LogDebug("Removed OBX segment at line {Index}", index + 1);
            }

            // Add new segments after removal (adjust positions for removed lines)
            foreach (var (position, segment) in newSegmentsToAdd.OrderByDescending(x => x.position))
            {
                // Adjust position based on how many lines were removed before this position
                var adjustedPosition = position;
                foreach (var removedIndex in obxLinesToRemove.Where(r => r < position))
                {
                    adjustedPosition--;
                }
                lines.Insert(adjustedPosition, segment);
                _logger.LogDebug("Inserted new segment at adjusted position {Position}: {Segment}", adjustedPosition, segment.Substring(0, Math.Min(20, segment.Length)));
            }

            return string.Join("\r\n", lines);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying OBX mapping with string manipulation");
            return messageString; // Return original if transformation fails
        }
    }

    private void ApplyFieldCleanup(IMessage message)
    {
        try
        {
            // Clean up PID segment - remove empty fields and fix formatting
            var pidSegment = (ISegment?)message.GetStructure("PID");
            if (pidSegment != null)
            {
                CleanupPidSegment(pidSegment);
            }

            // Clean up PV1 segment - remove empty fields and fix formatting
            var pv1Segment = (ISegment?)message.GetStructure("PV1");
            if (pv1Segment != null)
            {
                CleanupPv1Segment(pv1Segment);
            }

            // Clean up PV2 segment - remove empty fields and fix formatting
            var pv2Segment = (ISegment?)message.GetStructure("PV2");
            if (pv2Segment != null)
            {
                CleanupPv2Segment(pv2Segment);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying field cleanup");
        }
    }

    private void EnsureRequiredSegments(IMessage message)
    {
        try
        {
            // Get MSH segment to determine message type using generic approach
            var mshSegment = (ISegment)message.GetStructure("MSH");
            var messageTypeField = mshSegment.GetField(9, 0);
            var messageType = messageTypeField?.ToString()?.Split('^')[0] ?? string.Empty;

            _logger.LogDebug("Message type detected: {MessageType}", messageType);

            // For ADT messages, ensure EVN segment exists
            if (messageType != "ADT")
                return;
            try
            {
                message.GetStructure("EVN");
                _logger.LogDebug("EVN segment already exists");
            }
            catch
            {
                _logger.LogDebug("EVN segment missing, would create new one");
                // TODO: Implement EVN segment creation
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to ensure required segments");
        }
    }

    /// <summary>
    /// Clean up PID segment by removing empty fields and fixing formatting
    /// </summary>
    /// <param name="pidSegment">PID segment to clean up</param>
    private void CleanupPidSegment(ISegment pidSegment)
    {
        try
        {
            // This is a placeholder for PID-specific cleanup
            // The actual implementation would need to handle specific field transformations
            _logger.LogDebug("Cleaning up PID segment");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up PID segment");
        }
    }

    /// <summary>
    /// Clean up PV1 segment by removing empty fields and fixing formatting
    /// </summary>
    /// <param name="pv1Segment">PV1 segment to clean up</param>
    private void CleanupPv1Segment(ISegment pv1Segment)
    {
        try
        {
            // This is a placeholder for PV1-specific cleanup
            // The actual implementation would need to handle specific field transformations
            _logger.LogDebug("Cleaning up PV1 segment");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up PV1 segment");
        }
    }

    /// <summary>
    /// Clean up PV2 segment by removing empty fields and fixing formatting
    /// </summary>
    /// <param name="pv2Segment">PV2 segment to clean up</param>
    private void CleanupPv2Segment(ISegment pv2Segment)
    {
        try
        {
            // This is a placeholder for PV2-specific cleanup
            // The actual implementation would need to handle specific field transformations
            _logger.LogDebug("Cleaning up PV2 segment");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up PV2 segment");
        }
    }

    /// <summary>
    /// Apply specific PID segment enhancements according to enhancement rules
    /// </summary>
    /// <param name="pidLine">PID segment line</param>
    /// <returns>Enhanced PID segment line</returns>
    private string ApplyPidEnhancements(string pidLine)
    {
        try
        {
            var pidFields = pidLine.Split('|');
            
            // Patient Deceased Indicator: In PID-30, if the value is No or N, change it to Y
            if (pidFields.Length > 30)
            {
                var currentValue = pidFields[30];
                if (currentValue == "No" || currentValue == "N")
                {
                    pidFields[30] = "Y";
                    _logger.LogDebug("Updated PID-30 (Patient Deceased Indicator) from '{OldValue}' to 'Y'", currentValue);
                }
            }
            
            // Identifier Formatting (Assigning Authority): Within PID-3
            if (pidFields.Length > 3)
            {
                // If an assigning facility component (CX.9) contains the value medicom, ensure it is followed by two empty components
                if (pidFields[3].Contains("medicom||"))
                {
                    pidFields[3] = pidFields[3].Replace("medicom||", "medicom^^||");
                    _logger.LogDebug("Fixed medicom formatting in PID-3");
                }
                
                // Identifier Formatting (Quoting): For any identifier where Assigning Authority (CX.4) is MOI and Identifier Type Code (CX.5) is SSN
                // Ensure both values are enclosed in double quotes
                if (pidFields[3].Contains("^^^MOI^SSN"))
                {
                    pidFields[3] = pidFields[3].Replace("^^^MOI^SSN", "^^^\"MOI\"^\"SSN\"");
                    _logger.LogDebug("Added quotes to MOI and SSN in PID-3");
                }
            }
            
            // Expand Coded Values to Full Text
            // Administrative Sex: In PID-8, convert codes to full text
            if (pidFields.Length > 8)
            {
                var sexCode = pidFields[8];
                var expandedSex = sexCode switch
                {
                    "M" => "male",
                    "F" => "female",
                    _ => sexCode // Keep original if not recognized
                };
                if (expandedSex != sexCode)
                {
                    pidFields[8] = expandedSex;
                    _logger.LogDebug("Expanded PID-8 (Administrative Sex) from '{OldValue}' to '{NewValue}'", sexCode, expandedSex);
                }
            }
            
            // Marital Status: In PID-16, convert codes to full text
            if (pidFields.Length > 16)
            {
                var maritalCode = pidFields[16];
                var expandedMarital = maritalCode switch
                {
                    "S" => "Single",
                    "M" => "Married",
                    "D" => "Divorced",
                    "W" => "Widowed",
                    "A" => "Separated",
                    "P" => "Domestic Partner",
                    _ => maritalCode // Keep original if not recognized
                };
                if (expandedMarital != maritalCode)
                {
                    pidFields[16] = expandedMarital;
                    _logger.LogDebug("Expanded PID-16 (Marital Status) from '{OldValue}' to '{NewValue}'", maritalCode, expandedMarital);
                }
            }
            
            // Legacy fixes for backward compatibility
            var updatedLine = string.Join("|", pidFields);
            if (updatedLine.Contains("MRN~625664-I"))
            {
                updatedLine = updatedLine.Replace("MRN~625664-I", "MRN^~625664-I");
            }
            
            // Ensure PID segment has proper trailing pipes
            var finalFields = updatedLine.Split('|');
            if (finalFields.Length < 31) // PID should have at least 30 fields (PID + 29 fields)
            {
                var missingFields = 31 - finalFields.Length;
                updatedLine += new string('|', missingFields);
            }
            
            return updatedLine;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying PID enhancements");
            return pidLine; // Return original if enhancement fails
        }
    }

    /// <summary>
    /// Apply specific PV2 segment enhancements according to enhancement rules
    /// </summary>
    /// <param name="pv2Line">PV2 segment line</param>
    /// <returns>Enhanced PV2 segment line</returns>
    private string ApplyPv2Enhancements(string pv2Line)
    {
        try
        {
            var pv2Fields = pv2Line.Split('|');
            
            // Admit Reason: In PV2-20, convert codes to full text
            if (pv2Fields.Length > 20)
            {
                var admitReasonCode = pv2Fields[20];
                var expandedAdmitReason = admitReasonCode switch
                {
                    "Y" => "Yes",
                    "N" => "No",
                    "U" => "Unknown",
                    _ => admitReasonCode // Keep original if not recognized
                };
                if (expandedAdmitReason != admitReasonCode)
                {
                    pv2Fields[20] = expandedAdmitReason;
                    _logger.LogDebug("Expanded PV2-20 (Admit Reason) from '{OldValue}' to '{NewValue}'", admitReasonCode, expandedAdmitReason);
                }
            }
            
            return string.Join("|", pv2Fields);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying PV2 enhancements");
            return pv2Line; // Return original if enhancement fails
        }
    }

    /// <summary>
    /// Determine if a message should be enhanced based on MSH-9 (Message Type)
    /// </summary>
    /// <param name="messageString">HL7 message as string</param>
    /// <returns>True if message should be enhanced, false otherwise</returns>
    private bool ShouldEnhanceMessage(string messageString)
    {
        try
        {
            var lines = messageString.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
            var mshLine = lines.FirstOrDefault(l => l.StartsWith("MSH"));
            
            if (string.IsNullOrEmpty(mshLine))
            {
                _logger.LogWarning("MSH segment not found, skipping enhancement");
                return false;
            }

            _logger.LogInformation("MSH Line: {MshLine}", mshLine);
            var mshFields = mshLine.Split('|');
            _logger.LogInformation("MSH Fields count: {FieldCount}", mshFields.Length);
            
            if (mshFields.Length < 10)
            {
                _logger.LogWarning("MSH segment does not have enough fields, skipping enhancement");
                return false;
            }

            var messageType = mshFields[8]; // MSH-9 is at index 8
            _logger.LogInformation("Message type (MSH-9): '{MessageType}'", messageType);

            // Appropriate types for enhancement
            var enhancementTypes = new[]
            {
                "ADT^A04", // Register a patient
                "ADT^A08", // Update patient information
                "ADT^A28", // Add person information
                "ADT^A31", // Update person information
                "SIU^S12", // New appointment
                "SIU^S13"  // Reschedule appointment
            };

            // Inappropriate types (should NOT be enhanced)
            var skipTypes = new[]
            {
                "ADT^A34", // Merge patient information
                "SIU^S15", // Cancel appointment
                "SIU^S26"  // Cancel appointment notification
            };

            if (skipTypes.Contains(messageType))
            {
                _logger.LogInformation("Message type {MessageType} is inappropriate for enhancement (merge/cancel operation)", messageType);
                return false;
            }

            if (enhancementTypes.Contains(messageType))
            {
                _logger.LogInformation("Message type {MessageType} is appropriate for enhancement", messageType);
                return true;
            }

            // For other message types, log and decide based on configuration
            _logger.LogInformation("Message type {MessageType} is not explicitly listed, proceeding with enhancement", messageType);
            return true; // Default to enhancement for unlisted types
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking message type for enhancement eligibility");
            return true; // Default to enhancement on error
        }
    }
}