using System.Text;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NHapi.Base.Model;
using NHapi.Base.Parser;
using NHapi.Model.V28.Message;
using NHapi.Model.V28.Segment;
using Hl7MessageEnhancer.Exceptions;
using Hl7MessageEnhancer.Models;
using Hl7MessageEnhancer.Interfaces;

namespace Hl7MessageEnhancer.Services;

/// <summary>
/// File-based HL7 processing service that handles file I/O operations
/// Uses HL7MessageProcessor for core message processing logic
/// </summary>
public class Hl7Processor : IHl7Processor
{
    private readonly ILogger<Hl7Processor> _logger;
    private readonly string _sourceDirectory;
    private readonly string _outputDirectory;
    private readonly string _quarantineDirectory;
    private readonly bool _verbose;
    private readonly string _configurationName;
    private readonly ProcessingStatistics _statistics;
    private readonly List<MappingRule> _mappingRules;
    private readonly PipeParser _parser;
    private readonly string _errorLogPath;
    private readonly Hl7MessageProcessor _messageProcessor;

    /// <summary>
    /// Initializes a new instance of the HL7Processor class
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <param name="sourceDirectory">Source directory for raw HL7 files</param>
    /// <param name="outputDirectory">Output directory for enhanced files</param>
    /// <param name="verbose">Enable verbose logging</param>
    /// <param name="configurationName">Name of the mapping rules configuration to use</param>
    public Hl7Processor(ILogger<Hl7Processor> logger, string sourceDirectory, string outputDirectory, bool verbose = false, string configurationName = "DEFAULT")
    {
        _logger = logger;
        _sourceDirectory = sourceDirectory;
        _outputDirectory = outputDirectory;
        _quarantineDirectory = "quarantine";
        _verbose = verbose;
        _configurationName = configurationName;
        _statistics = new ProcessingStatistics();
        _mappingRules = [];
        _parser = new PipeParser();
        _errorLogPath = "error_details.log";
        
        // Create a message processor for core HL7 processing logic
        var messageProcessorLogger = LoggerFactory.Create(builder => 
            builder.AddConsole().SetMinimumLevel(verbose ? LogLevel.Debug : LogLevel.Warning))
            .CreateLogger<Hl7MessageProcessor>();
        _messageProcessor = new Hl7MessageProcessor(messageProcessorLogger, configurationName);

        LoadConfiguration();
        CreateDirectories();
    }

    /// <summary>
    /// Gets the current processing statistics
    /// </summary>
    public ProcessingStatistics Statistics => _statistics;

    /// <summary>
    /// Process a single HL7 message from string content
    /// </summary>
    /// <param name="hl7Content">Raw HL7 message content</param>
    /// <returns>Enhanced HL7 message as a string</returns>
    public string ProcessHl7Message(string hl7Content)
    {
        return _messageProcessor.ProcessHl7Message(hl7Content);
    }

    /// <summary>
    /// Process a single HL7 message and return the NHapi message object
    /// </summary>
    /// <param name="hl7Content">Raw HL7 message content</param>
    /// <returns>Enhanced HL7 message as IMessage</returns>
    public IMessage ProcessHl7MessageToObject(string hl7Content)
    {
        return _messageProcessor.ProcessHl7MessageToObject(hl7Content);
    }

    /// <summary>
    /// Load mapping rules and configuration
    /// </summary>
    private void LoadConfiguration()
    {
        try
        {
            // Load hardcoded mapping rules based on configuration
            var rules = MappingRulesProvider.GetMappingRulesForConfiguration(_configurationName);
            _mappingRules.AddRange(rules);
            _logger.LogInformation("Loaded {Count} hardcoded mapping rules for configuration '{Configuration}'", _mappingRules.Count, _configurationName);
        }
        catch (Exception ex)
        {
            throw new Hl7ProcessingException($"Failed to load configuration: {ex.Message}", "CONFIG_ERROR", ex);
        }
    }

    /// <summary>
    /// Create necessary directories and clear output directory if it exists
    /// </summary>
    private void CreateDirectories()
    {
        try
        {
            // Only clear output directory if it's different from current directory and source directory
            // This prevents deleting files in use during testing
            var currentDir = Directory.GetCurrentDirectory();
            var outputFullPath = Path.GetFullPath(_outputDirectory);
            var sourceFullPath = Path.GetFullPath(_sourceDirectory);
            var currentFullPath = Path.GetFullPath(currentDir);

            if (Directory.Exists(_outputDirectory) &&
                !string.Equals(outputFullPath, currentFullPath, StringComparison.OrdinalIgnoreCase) &&
                !string.Equals(outputFullPath, sourceFullPath, StringComparison.OrdinalIgnoreCase))
            {
                Directory.Delete(_outputDirectory, true);
                _logger.LogInformation("Cleared existing output directory: {OutputDirectory}", _outputDirectory);
            }
            else if (Directory.Exists(_outputDirectory))
            {
                // If output directory is same as current or source directory, only clean HL7 files
                CleanHl7FilesFromDirectory(_outputDirectory);
                _logger.LogInformation("Cleaned HL7 files from output directory: {OutputDirectory}", _outputDirectory);
            }

            // Create directories
            Directory.CreateDirectory(_outputDirectory);
            Directory.CreateDirectory(_quarantineDirectory);
        }
        catch (Exception ex)
        {
            throw new Hl7ProcessingException($"Failed to create directories: {ex.Message}", "DIRECTORY_ERROR", ex);
        }
    }

    /// <summary>
    /// Clean only HL7 files from a directory without deleting the directory itself
    /// </summary>
    /// <param name="directory">Directory to clean</param>
    private void CleanHl7FilesFromDirectory(string directory)
    {
        try
        {
            var hl7Files = Directory.GetFiles(directory, "*.hl7", SearchOption.TopDirectoryOnly);
            foreach (var file in hl7Files)
            {
                try
                {
                    File.Delete(file);
                    _logger.LogDebug("Deleted HL7 file: {File}", file);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Could not delete HL7 file: {File}", file);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Could not clean HL7 files from directory: {Directory}", directory);
        }
    }

    /// <summary>
    /// Find all HL7 files in the source directory recursively
    /// </summary>
    /// <returns>List of HL7 file paths</returns>
    private List<string> FindHl7Files()
    {
        try
        {
            return Directory.GetFiles(_sourceDirectory, "*.hl7", SearchOption.AllDirectories).ToList();
        }
        catch (Exception ex)
        {
            throw new Hl7ProcessingException($"Failed to scan directory {_sourceDirectory}: {ex.Message}", "DIRECTORY_ERROR", ex);
        }
    }

    /// <summary>
    /// Parse HL7 file into message object
    /// </summary>
    /// <param name="filePath">Path to the HL7 file</param>
    /// <returns>Parsed HL7 message</returns>
    private IMessage ParseHl7Message(string filePath)
    {
        try
        {
            var content = File.ReadAllText(filePath, Encoding.UTF8).Trim();

            if (string.IsNullOrEmpty(content))
            {
                throw new Hl7ProcessingException("Empty file", "HL7_PARSING_FAILURE", filePath);
            }

            // Preprocess HL7 content: ensure proper segment separators
            // NHapi expects \r as segment separator
            if (content.Contains('\n') && !content.Contains('\r'))
            {
                content = content.Replace('\n', '\r');
            }
            else if (!content.Contains('\n') && !content.Contains('\r'))
            {
                // Split by segment identifiers and rejoin with carriage returns
                var segmentPattern = @"(?=MSH|EVN|PID|PV1|OBX|OBR|NTE|AL1|DG1|PR1|GT1|IN1|IN2|IN3|ACC|UB1|UB2|ZQA|ZFM)";
                content = Regex.Replace(content, segmentPattern, "\r").Trim();
            }

            // Parse HL7 message
            var message = _parser.Parse(content);

            // Validate MSH segment exists
            if (message.GetStructure("MSH") == null)
            {
                throw new Hl7ProcessingException("MSH segment not found", "HL7_PARSING_FAILURE", filePath);
            }

            return message;
        }
        catch (Hl7ProcessingException)
        {
            throw;
        }
        catch (Exception ex)
        {
            throw new Hl7ProcessingException($"Failed to parse HL7 message: {ex.Message}", "HL7_PARSING_FAILURE", ex, filePath);
        }
    }

    /// <summary>
    /// Apply standard HL7 enhancements to the message
    /// </summary>
    /// <param name="message">HL7 message to enhance</param>
    /// <returns>Enhanced HL7 message</returns>
    private IMessage ApplyHl7Enhancements(IMessage message)
    {
        try
        {
            // Convert message to string for manipulation using the parser
            var messageString = _parser.Encode(message);
            _logger.LogDebug("Original message string: {MessageString}", messageString);

            // Apply string-based transformations
            messageString = ApplyStringBasedTransformations(messageString);
            _logger.LogDebug("Message after string transformations: {MessageString}", messageString);

            // Parse the modified message back
            var enhancedMessage = _parser.Parse(messageString);

            // Apply OBX mapping rules using string manipulation
            var finalMessageString = ApplyObxMappingWithStringManipulation(messageString);

            _logger.LogDebug("Message after OBX mapping: {MessageString}", finalMessageString);

            // Parse the final message
            var finalMessage = _parser.Parse(finalMessageString);

            // Apply additional field cleanup and transformations
            ApplyFieldCleanup(finalMessage);

            // Ensure required segments exist for ADT messages
            EnsureRequiredSegments(finalMessage);

            return finalMessage;
        }
        catch (Exception ex)
        {
            throw new Hl7ProcessingException($"Failed to apply enhancements: {ex.Message}", "PROCESSING_ERROR", ex);
        }
    }

    /// <summary>
    /// Apply string-based transformations to the HL7 message
    /// </summary>
    /// <param name="messageString">HL7 message as string</param>
    /// <returns>Modified HL7 message string</returns>
    private string ApplyStringBasedTransformations(string messageString)
    {
        try
        {
            var lines = messageString.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

            for (int i = 0; i < lines.Length; i++)
            {
                if (lines[i].StartsWith("MSH"))
                {
                    // Update MSH segment
                    lines[i] = UpdateMshSegment(lines[i]);
                }
            }

            return string.Join("\r\n", lines);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying string-based transformations");
            return messageString; // Return original if transformation fails
        }
    }

    /// <summary>
    /// Update MSH segment fields
    /// </summary>
    /// <param name="mshLine">MSH segment line</param>
    /// <returns>Updated MSH segment line</returns>
    private string UpdateMshSegment(string mshLine)
    {
        try
        {
            var fields = mshLine.Split('|');

            if (fields.Length > 11)
            {
                // Update version to 2.8 (field 12, array index 11)
                fields[11] = "2.8";
                _logger.LogDebug("Updated MSH-12 (Version) to '2.8'");
            }

            if (fields.Length > 10)
            {
                // Set processing ID to Production (field 11, array index 10)
                fields[10] = "P";
                _logger.LogDebug("Updated MSH-11 (Processing ID) to 'P'");
            }

            return string.Join("|", fields);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating MSH segment");
            return mshLine; // Return original if update fails
        }
    }

    /// <summary>
    /// Apply OBX mapping rules using string manipulation
    /// </summary>
    /// <param name="messageString">HL7 message as string</param>
    /// <returns>Modified HL7 message string with OBX mappings applied</returns>
    private string ApplyObxMappingWithStringManipulation(string messageString)
    {
        try
        {
            var lines = messageString.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries).ToList();
            var obxLinesToRemove = new List<int>();
            var newSegmentsToAdd = new List<(int position, string segment)>();

            // Process each OBX segment
            for (int i = 0; i < lines.Count; i++)
            {
                if (!lines[i].StartsWith("OBX"))
                    continue;

                var obxFields = lines[i].Split('|');
                if (obxFields.Length < 6)
                    continue;

                var obxValue = obxFields[3]; // OBX-3.1 is at index 3
                var obxDataValue = obxFields.Length > 5 ? obxFields[5] : ""; // OBX-5 is at index 5

                _logger.LogDebug("Processing OBX segment: OBX-3.1='{ObxValue}', OBX-5='{ObxDataValue}'", obxValue, obxDataValue);

                // Find matching mapping rule
                var matchingRule = _mappingRules.FirstOrDefault(r => r.ObxValue == obxValue);
                if (matchingRule == null)
                    continue;

                // Apply value mapping if specified
                var valueToMap = obxDataValue;
                if (matchingRule.ValueMapping != null)
                {
                    if (matchingRule.ValueMapping.TryGetValue("*", out var wildcardValue))
                    {
                        valueToMap = wildcardValue;
                    }
                    else if (matchingRule.ValueMapping.TryGetValue(valueToMap, out var mappedValue))
                    {
                        valueToMap = mappedValue;
                    }
                }

                // Apply the mapping
                if (!string.IsNullOrEmpty(matchingRule.TargetSegment))
                {
                    ApplyStringBasedMapping(lines, matchingRule, valueToMap, newSegmentsToAdd);
                }

                // Mark for removal if specified
                if (matchingRule.RemoveOriginal)
                {
                    obxLinesToRemove.Add(i);
                }

                _logger.LogDebug("Applied mapping rule for {ObxValue} -> {TargetSegment}-{TargetField} = '{Value}'",
                    matchingRule.ObxValue, matchingRule.TargetSegment, matchingRule.TargetField, valueToMap);
            }

            // Remove OBX segments first (in reverse order to maintain indices)
            foreach (var index in obxLinesToRemove.OrderByDescending(x => x))
            {
                lines.RemoveAt(index);
                _logger.LogDebug("Removed OBX segment at line {Index}", index + 1);
            }

            // Add new segments after removal (adjust positions for removed lines)
            foreach (var (position, segment) in newSegmentsToAdd.OrderByDescending(x => x.position))
            {
                // Adjust position based on how many lines were removed before this position
                var adjustedPosition = position;
                foreach (var removedIndex in obxLinesToRemove.Where(r => r < position))
                {
                    adjustedPosition--;
                }
                lines.Insert(adjustedPosition, segment);
                _logger.LogDebug("Inserted new segment at adjusted position {Position}: {Segment}", adjustedPosition, segment.Substring(0, Math.Min(20, segment.Length)));
            }

            return string.Join("\r", lines);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying OBX mapping with string manipulation");
            return messageString; // Return original if mapping fails
        }
    }

    /// <summary>
    /// Apply string-based mapping for a specific rule
    /// </summary>
    /// <param name="lines">List of message lines</param>
    /// <param name="rule">Mapping rule to apply</param>
    /// <param name="value">Value to map</param>
    /// <param name="newSegmentsToAdd">List to add new segments to</param>
    private void ApplyStringBasedMapping(List<string> lines, MappingRule rule, string value, List<(int position, string segment)> newSegmentsToAdd)
    {
        try
        {
            // Find existing target segment
            var targetLineIndex = -1;
            for (int i = 0; i < lines.Count; i++)
            {
                if (lines[i].StartsWith(rule.TargetSegment))
                {
                    targetLineIndex = i;
                    break;
                }
            }

            if (targetLineIndex >= 0)
            {
                // Update existing segment
                lines[targetLineIndex] = UpdateSegmentField(lines[targetLineIndex], rule.TargetField, value);
                _logger.LogDebug("Updated existing {TargetSegment} segment at line {LineIndex}", rule.TargetSegment, targetLineIndex + 1);
            }
            else
            {
                // Create new segment
                var newSegment = CreateNewSegment(rule.TargetSegment, rule.TargetField, value);
                if (!string.IsNullOrEmpty(newSegment))
                {
                    // Find appropriate position to insert the new segment
                    var insertPosition = FindInsertPosition(lines, rule.TargetSegment);
                    newSegmentsToAdd.Add((insertPosition, newSegment));
                    _logger.LogDebug("Created new {TargetSegment} segment to be inserted at position {Position}", rule.TargetSegment, insertPosition);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying string-based mapping for {TargetSegment}", rule.TargetSegment);
        }
    }

    /// <summary>
    /// Update a specific field in a segment
    /// </summary>
    /// <param name="segmentLine">Segment line</param>
    /// <param name="fieldPath">Field path</param>
    /// <param name="value">Value to set</param>
    /// <returns>Updated segment line</returns>
    private string UpdateSegmentField(string segmentLine, string fieldPath, string value)
    {
        try
        {
            var fields = segmentLine.Split('|');
            var fieldParts = fieldPath.Split('.');
            var fieldNumber = int.Parse(fieldParts[0]);

            // Ensure we have enough fields
            while (fields.Length <= fieldNumber)
            {
                Array.Resize(ref fields, fields.Length + 1);
                fields[fields.Length - 1] = "";
            }

            if (fieldParts.Length == 1)
            {
                // Simple field update
                // Apply padding for PD1-3 field to match expected format
                if (segmentLine.StartsWith("PD1") && fieldNumber == 3)
                {
                    fields[fieldNumber] = value.PadRight(33, '_');
                }
                else
                {
                    fields[fieldNumber] = value;
                }
            }
            else if (fieldParts.Length == 3)
            {
                // Complex field path like "3.1.6" (field 3, repetition 1, component 6)
                var repetitionIndex = int.Parse(fieldParts[1]) - 1;
                var componentIndex = int.Parse(fieldParts[2]) - 1;

                var fieldValue = fields[fieldNumber];
                var repetitions = fieldValue.Split('~');

                // Ensure we have enough repetitions
                while (repetitions.Length <= repetitionIndex)
                {
                    Array.Resize(ref repetitions, repetitions.Length + 1);
                    repetitions[repetitions.Length - 1] = "";
                }

                if (repetitionIndex < repetitions.Length)
                {
                    var components = repetitions[repetitionIndex].Split('^');

                    // Ensure we have enough components
                    while (components.Length <= componentIndex)
                    {
                        Array.Resize(ref components, components.Length + 1);
                        components[components.Length - 1] = "";
                    }

                    if (componentIndex < components.Length)
                    {
                        components[componentIndex] = value;
                        repetitions[repetitionIndex] = string.Join("^", components);
                        fields[fieldNumber] = string.Join("~", repetitions);
                    }
                }
            }

            return string.Join("|", fields);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating segment field {FieldPath}", fieldPath);
            return segmentLine;
        }
    }

    /// <summary>
    /// Create a new segment with the specified field value
    /// </summary>
    /// <param name="segmentName">Name of the segment to create</param>
    /// <param name="fieldPath">Field path</param>
    /// <param name="value">Value to set</param>
    /// <returns>New segment string</returns>
    private string CreateNewSegment(string segmentName, string fieldPath, string value)
    {
        try
        {
            switch (segmentName)
            {
                case "ROL":
                    // ROL|1|AD|PP^Primary Care Provider^HL70443|<physician_name>
                    return $"ROL|1|AD|PP^Primary Care Provider^HL70443|{value}";

                case "PD1":
                    // PD1|||<primary_facility>|||||
                    // Add padding to match expected format (33 characters total with underscores)
                    var paddedValue = value.PadRight(33, '_');
                    return $"PD1|||{paddedValue}|||||";

                default:
                    _logger.LogWarning("Unsupported segment creation: {SegmentName}", segmentName);
                    return "";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating new segment {SegmentName}", segmentName);
            return "";
        }
    }

    /// <summary>
    /// Find the appropriate position to insert a new segment
    /// </summary>
    /// <param name="lines">List of message lines</param>
    /// <param name="segmentName">Name of the segment to insert</param>
    /// <returns>Insert position</returns>
    private int FindInsertPosition(List<string> lines, string segmentName)
    {
        try
        {
            switch (segmentName)
            {
                case "ROL":
                    // ROL should be inserted after PID
                    for (int i = 0; i < lines.Count; i++)
                    {
                        if (lines[i].StartsWith("PID"))
                        {
                            return i + 1;
                        }
                    }
                    break;

                case "PD1":
                    // PD1 should be inserted after ROL if it exists, otherwise after PID
                    for (int i = 0; i < lines.Count; i++)
                    {
                        if (lines[i].StartsWith("ROL"))
                        {
                            return i + 1;
                        }
                    }
                    for (int i = 0; i < lines.Count; i++)
                    {
                        if (lines[i].StartsWith("PID"))
                        {
                            return i + 1;
                        }
                    }
                    break;
            }

            // Default: insert before PV1 if it exists, otherwise at the end
            for (int i = 0; i < lines.Count; i++)
            {
                if (lines[i].StartsWith("PV1"))
                {
                    return i;
                }
            }

            return lines.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error finding insert position for {SegmentName}", segmentName);
            return lines.Count;
        }
    }

    /// <summary>
    /// Apply OBX segment mapping rules
    /// </summary>
    /// <param name="message">HL7 message to process</param>
    private void ApplyObxMapping(IMessage message)
    {
        try
        {
            var obxSegments = new List<(int index, ISegment segment)>();
            var segmentsToRemove = new List<ISegment>();

            // Collect all OBX segments
            var structures = message.GetAll("OBX");
            for (int i = 0; i < structures.Length; i++)
            {
                if (structures[i] is ISegment segment)
                {
                    obxSegments.Add((i, segment));
                }
            }

            // Apply mapping rules
            foreach (var rule in _mappingRules)
            {
                foreach (var (_, obxSegment) in obxSegments)
                {
                    if (!ProcessObxSegment(message, obxSegment, rule))
                        continue;
                    if (!rule.RemoveOriginal)
                        continue;
                    segmentsToRemove.Add(obxSegment);
                }
            }

            // Remove OBX segments marked for removal
            foreach (var segment in segmentsToRemove)
            {
                // Note: NHapi doesn't have a direct remove method, so we'll need to work around this
                _logger.LogDebug("Marked OBX segment for removal: {Segment}", segment);
            }
        }
        catch (Exception ex)
        {
            throw new Hl7ProcessingException($"Failed to apply OBX mapping: {ex.Message}", "MAPPING_RULE_ERROR", ex);
        }
    }

    /// <summary>
    /// Process a single OBX segment against a mapping rule
    /// </summary>
    /// <param name="message">HL7 message</param>
    /// <param name="obxSegment">OBX segment to process</param>
    /// <param name="rule">Mapping rule to apply</param>
    /// <returns>True if the rule was applied</returns>
    private bool ProcessObxSegment(IMessage message, ISegment obxSegment, MappingRule rule)
    {
        try
        {
            // Extract OBX-3.1 (first component of observation identifier) using generic approach
            var obx3Field = obxSegment.GetField(3, 0);
            var obxFieldValue = obx3Field?.ToString()?.Split('^')[0] ?? string.Empty;

            if (rule.ObxValue != obxFieldValue)
                return false;
            // Get the value to map (OBX-5)
            var obx5Field = obxSegment.GetField(5, 0);
            var valueToMap = obx5Field?.ToString()?.Trim() ?? string.Empty;

            // Apply value mapping if specified
            if (rule.ValueMapping != null)
            {
                // Check for a wildcard mapping first
                if (rule.ValueMapping.TryGetValue("*", out var wildcardValue))
                {
                    valueToMap = wildcardValue;
                    _logger.LogDebug("Applied wildcard value mapping: {ObxValue} -> '{ValueToMap}'", rule.ObxValue, valueToMap);
                }
                // Otherwise, check for a specific mapping
                else if (rule.ValueMapping.TryGetValue(valueToMap, out var mappedValue))
                {
                    var originalValue = valueToMap;
                    valueToMap = mappedValue;
                    _logger.LogDebug("Applied value mapping: {OriginalValue} -> '{ValueToMap}'", originalValue, valueToMap);
                }
            }

            // Only proceed with mapping if value is not empty
            if (!string.IsNullOrWhiteSpace(valueToMap))
            {
                MapValueToTargetSegment(message, rule, valueToMap);
                return true;
            }
            _logger.LogDebug("Skipping mapping for {ObxValue} - empty or whitespace-only value: '{ValueToMap}'", rule.ObxValue, valueToMap);

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing OBX segment for rule {ObxValue}", rule.ObxValue);
            return false;
        }
    }

    /// <summary>
    /// Map a value to the target segment and field
    /// </summary>
    /// <param name="message">HL7 message</param>
    /// <param name="rule">Mapping rule</param>
    /// <param name="value">Value to map</param>
    private void MapValueToTargetSegment(IMessage message, MappingRule rule, string value)
    {
        try
        {
            // Find or create target segment
            ISegment? targetSegment = null;

            try
            {
                targetSegment = (ISegment?)message.GetStructure(rule.TargetSegment);
            }
            catch
            {
                // Segment doesn't exist
            }

            // Create segment if it doesn't exist
            targetSegment ??= CreateTargetSegment(message, rule.TargetSegment);

            if (targetSegment != null)
            {
                SetFieldValue(targetSegment, rule.TargetField, value);
                _logger.LogDebug("Mapped {ObxValue} = '{Value}' to {TargetSegment}-{TargetField}",
                    rule.ObxValue, value, rule.TargetSegment, rule.TargetField);
            }
            else
            {
                _logger.LogWarning("Could not create or find target segment {TargetSegment} for {ObxValue}",
                    rule.TargetSegment, rule.ObxValue);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error mapping value to target segment {TargetSegment}", rule.TargetSegment);
        }
    }

    /// <summary>
    /// Create a target segment if it doesn't exist
    /// </summary>
    /// <param name="message">HL7 message</param>
    /// <param name="segmentName">Name of the segment to create</param>
    /// <returns>Created segment or null if not supported</returns>
    private ISegment? CreateTargetSegment(IMessage message, string segmentName)
    {
        // For now, we'll log that segment creation is needed
        // Full implementation would require more complex NHapi manipulation
        _logger.LogDebug("Target segment {SegmentName} creation needed", segmentName);
        return null;
    }

    /// <summary>
    /// Apply additional field cleanup and transformations
    /// </summary>
    /// <param name="message">HL7 message to process</param>
    private void ApplyFieldCleanup(IMessage message)
    {
        try
        {
            // Clean up PID segment - remove empty fields and fix formatting
            var pidSegment = (ISegment?)message.GetStructure("PID");
            if (pidSegment != null)
            {
                CleanupPidSegment(pidSegment);
            }

            // Clean up PV1 segment - remove empty fields and fix formatting
            var pv1Segment = (ISegment?)message.GetStructure("PV1");
            if (pv1Segment != null)
            {
                CleanupPv1Segment(pv1Segment);
            }

            // Clean up PV2 segment - remove empty fields and fix formatting
            var pv2Segment = (ISegment?)message.GetStructure("PV2");
            if (pv2Segment != null)
            {
                CleanupPv2Segment(pv2Segment);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying field cleanup");
        }
    }

    /// <summary>
    /// Clean up PID segment by removing empty fields and fixing formatting
    /// </summary>
    /// <param name="pidSegment">PID segment to clean up</param>
    private void CleanupPidSegment(ISegment pidSegment)
    {
        try
        {
            // This is a placeholder for PID-specific cleanup
            // The actual implementation would need to handle specific field transformations
            _logger.LogDebug("Cleaning up PID segment");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up PID segment");
        }
    }

    /// <summary>
    /// Clean up PV1 segment by removing empty fields and fixing formatting
    /// </summary>
    /// <param name="pv1Segment">PV1 segment to clean up</param>
    private void CleanupPv1Segment(ISegment pv1Segment)
    {
        try
        {
            // This is a placeholder for PV1-specific cleanup
            // The actual implementation would need to handle specific field transformations
            _logger.LogDebug("Cleaning up PV1 segment");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up PV1 segment");
        }
    }

    /// <summary>
    /// Clean up PV2 segment by removing empty fields and fixing formatting
    /// </summary>
    /// <param name="pv2Segment">PV2 segment to clean up</param>
    private void CleanupPv2Segment(ISegment pv2Segment)
    {
        try
        {
            // This is a placeholder for PV2-specific cleanup
            // The actual implementation would need to handle specific field transformations
            _logger.LogDebug("Cleaning up PV2 segment");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up PV2 segment");
        }
    }

    /// <summary>
    /// Set a field value in a segment using NHapi's Parse method
    /// </summary>
    /// <param name="segment">Target segment</param>
    /// <param name="fieldPath">Field path (e.g., "3.x.8", "4", "30")</param>
    /// <param name="value">Value to set</param>
    private void SetFieldValue(ISegment segment, string fieldPath, string value)
    {
        try
        {
            var fieldParts = fieldPath.Split('.');
            var fieldNumber = int.Parse(fieldParts[0]);

            // Get the field using NHapi's GetField method
            var field = segment.GetField(fieldNumber, 0);

            if (field != null)
            {
                // Use the Parse method to set the value
                var fieldType = field.GetType();
                var parseMethod = fieldType.GetMethod("Parse", new[] { typeof(string) });

                if (parseMethod != null)
                {
                    parseMethod.Invoke(field, new object[] { value });
                    _logger.LogDebug("Set field {FieldPath} to '{Value}' in segment {SegmentName} using Parse method",
                        fieldPath, value, segment.GetStructureName());
                    return;
                }

                // Fallback: try to set the Value property
                var valueProperty = fieldType.GetProperty("Value");
                if (valueProperty != null && valueProperty.CanWrite)
                {
                    valueProperty.SetValue(field, value);
                    _logger.LogDebug("Set field {FieldPath} to '{Value}' in segment {SegmentName} using Value property",
                        fieldPath, value, segment.GetStructureName());
                    return;
                }
            }

            _logger.LogWarning("Could not set field {FieldPath} to '{Value}' in segment {SegmentName}",
                fieldPath, value, segment.GetStructureName());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to set field {FieldPath} to '{Value}' in segment {SegmentName}",
                fieldPath, value, segment.GetStructureName());
        }
    }



    /// <summary>
    /// Ensure required segments exist for specific message types
    /// </summary>
    /// <param name="message">HL7 message to process</param>
    private void EnsureRequiredSegments(IMessage message)
    {
        try
        {
            // Get MSH segment to determine message type using generic approach
            var mshSegment = (ISegment)message.GetStructure("MSH");
            var messageTypeField = mshSegment.GetField(9, 0);
            var messageType = messageTypeField?.ToString()?.Split('^')[0] ?? string.Empty;

            _logger.LogDebug("Message type detected: {MessageType}", messageType);

            // For ADT messages, ensure EVN segment exists
            if (messageType != "ADT")
                return;
            try
            {
                message.GetStructure("EVN");
                _logger.LogDebug("EVN segment already exists");
            }
            catch
            {
                _logger.LogDebug("EVN segment missing, would create new one");
                // TODO: Implement EVN segment creation
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to ensure required segments");
        }
    }

    /// <summary>
    /// Save enhanced message preserving directory structure
    /// </summary>
    /// <param name="message">Enhanced HL7 message</param>
    /// <param name="originalPath">Original file path</param>
    private void SaveEnhancedMessage(IMessage message, string originalPath)
    {
        try
        {
            // Convert message back to string with proper HL7 formatting
            var enhancedContent = _parser.Encode(message);

            // Fix NHapi encoding issues: replace underscores with proper empty field separators
            enhancedContent = FixNHapiEncodingIssues(enhancedContent);

            // Use the string overload
            SaveEnhancedMessage(enhancedContent, originalPath);
        }
        catch (Exception ex)
        {
            throw new Hl7ProcessingException($"Failed to save enhanced message: {ex.Message}", "FILE_WRITE_ERROR", ex, originalPath);
        }
    }

    /// <summary>
    /// Save enhanced HL7 message content to file
    /// </summary>
    /// <param name="enhancedContent">Enhanced HL7 message content as string</param>
    /// <param name="originalPath">Original file path</param>
    private void SaveEnhancedMessage(string enhancedContent, string originalPath)
    {
        try
        {
            // Calculate relative path from source directory
            var relativePath = Path.GetRelativePath(_sourceDirectory, originalPath);
            var outputPath = Path.Combine(_outputDirectory, relativePath);

            // Create parent directories
            var outputDir = Path.GetDirectoryName(outputPath);
            if (!string.IsNullOrEmpty(outputDir))
            {
                Directory.CreateDirectory(outputDir);
            }

            // Write to file
            File.WriteAllText(outputPath, enhancedContent, Encoding.UTF8);

            _logger.LogInformation("Enhanced message saved: {OutputPath}", outputPath);
        }
        catch (Exception ex)
        {
            throw new Hl7ProcessingException($"Failed to save enhanced message: {ex.Message}", "FILE_WRITE_ERROR", ex, originalPath);
        }
    }

    /// <summary>
    /// Fix NHapi encoding issues where empty fields are represented as underscores
    /// </summary>
    /// <param name="encodedMessage">The encoded HL7 message from NHapi</param>
    /// <returns>Fixed HL7 message with proper field separators</returns>
    private string FixNHapiEncodingIssues(string encodedMessage)
    {
        try
        {
            var lines = encodedMessage.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

            for (int i = 0; i < lines.Length; i++)
            {
                var line = lines[i];

                // Fix PD1 segment specifically - ensure it has the correct number of trailing pipes
                if (line.StartsWith("PD1|"))
                {
                    _logger.LogDebug("Found PD1 segment: {Segment}", line);
                    // PD1 should have format: PD1|||<value>|||||
                    // Count current fields
                    var fields = line.Split('|');
                    _logger.LogDebug("PD1 fields count: {Count}, Fields: {Fields}", fields.Length, string.Join(", ", fields));
                    
                    // Extract the value from field 3 (index 3) if it exists
                    var field3Value = fields.Length > 3 ? fields[3] : "";
                    
                    // Always ensure PD1 has exactly 8 fields (PD1 + 7 fields)
                    // This fixes the issue where NHapi removes trailing empty fields
                    var fixedPd1 = $"PD1|||{field3Value}|||||";
                    lines[i] = fixedPd1;
                    _logger.LogDebug("Fixed PD1 segment format: {FixedSegment}", fixedPd1);
                }
                
                // Additional fix for PD1 segments that may have been truncated by NHapi
                // This runs after the main PD1 fix to ensure trailing pipes are preserved
                if (line.StartsWith("PD1|"))
                {
                    _logger.LogDebug("Processing PD1 line before fix: {Line}", line);
                    var fields = line.Split('|');
                    if (fields.Length >= 4)
                    {
                        var field3Value = fields[3];
                        // Force the correct format regardless of current state
                        lines[i] = $"PD1|||{field3Value}|||||";
                        _logger.LogDebug("Ensured PD1 segment has correct trailing pipes: {FixedSegment}", lines[i]);
                    }
                }

                // Replace sequences of underscores with proper empty field separators
                // NHapi tends to use underscores for empty fields, but HL7 standard uses pipes

                // Replace multiple consecutive underscores with empty fields (pipes only)
                line = Regex.Replace(line, @"_{2,}", match => new string('|', match.Length - 1));

                // Replace single underscores between pipes with empty (remove the underscore)
                line = Regex.Replace(line, @"\|_\|", "||");
                
                // Fix specific PV1 segment issues
                if (line.StartsWith("PV1|"))
                {
                    line = line.Replace("DR NBR~", "DR NBR^~");
                    line = line.Replace("External Identifier|||", "External Identifier^|||");
                }
                
                // Fix specific PID segment issue
                if (line.StartsWith("PID|"))
                {
                    line = line.Replace("MRN~625664-I", "MRN^~625664-I");
                    line = line.Replace("medicom||", "medicom^^||");
                }

                // Replace underscores at the end of segments
                // Special handling for PD1 segments to preserve correct trailing pipe count
                if (line.StartsWith("PD1|"))
                {
                    // For PD1 segments, ensure we have the correct format after underscore replacement
                    line = Regex.Replace(line, @"_+$", "");
                    var fields = line.Split('|');
                    if (fields.Length >= 4)
                    {
                        var field3Value = fields[3];
                        line = $"PD1|||{field3Value}|||||";
                    }
                }
                else
                {
                    line = Regex.Replace(line, @"_+$", match => new string('|', match.Length));
                }

                // Debug logging for PD1 segments after all processing
                if (line.StartsWith("PD1|"))
                {
                    _logger.LogDebug("Final PD1 line after all regex processing: {Line}", line);
                }

                lines[i] = line;
            }

            var fixedMessage = string.Join("\r\n", lines);
            _logger.LogDebug("Fixed NHapi encoding issues in message");
            return fixedMessage;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to fix NHapi encoding issues, returning original message");
            return encodedMessage;
        }
    }

    /// <summary>
    /// Move problematic file to quarantine with error details
    /// </summary>
    /// <param name="filePath">Path to the problematic file</param>
    /// <param name="error">The processing error</param>
    private void QuarantineFile(string filePath, Hl7ProcessingException error)
    {
        try
        {
            // Calculate relative path
            var relativePath = Path.GetRelativePath(_sourceDirectory, filePath);
            var quarantinePath = Path.Combine(_quarantineDirectory, relativePath);

            // Create parent directories
            var quarantineDir = Path.GetDirectoryName(quarantinePath);
            if (!string.IsNullOrEmpty(quarantineDir))
            {
                Directory.CreateDirectory(quarantineDir);
            }

            // Copy file to quarantine
            File.Copy(filePath, quarantinePath, true);

            // Create error details file
            var errorFile = Path.ChangeExtension(quarantinePath, ".error.json");
            var errorDetails = new
            {
                original_path = filePath,
                error_code = error.ErrorCode,
                error_message = error.Message,
                timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            };

            File.WriteAllText(errorFile, JsonConvert.SerializeObject(errorDetails, Formatting.Indented));

            _statistics.FilesQuarantined++;
            _logger.LogWarning("File quarantined: {QuarantinePath}", quarantinePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to quarantine file {FilePath}", filePath);
        }
    }

    /// <summary>
    /// Log error in machine-readable JSON format
    /// </summary>
    /// <param name="error">The processing error</param>
    /// <param name="context">Additional context information</param>
    private void LogError(Hl7ProcessingException error, Dictionary<string, object>? context = null)
    {
        var errorEntry = new
        {
            timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            level = "error",
            message = error.Message,
            filePath = error.FilePath,
            errorCode = error.ErrorCode,
            errorContext = context ?? new Dictionary<string, object>(),
            service = "hl7-enhancer"
        };

        var json = JsonConvert.SerializeObject(errorEntry);
        File.AppendAllText(_errorLogPath, json + Environment.NewLine);
    }

    /// <summary>
    /// Process a single HL7 file
    /// </summary>
    /// <param name="filePath">Path to the HL7 file</param>
    /// <returns>True if processing was successful</returns>
    public bool ProcessFile(string filePath)
    {
        try
        {
            _logger.LogInformation("Processing: {FilePath}", filePath);

            // Read file content
            var fileContent = File.ReadAllText(filePath, Encoding.UTF8);
            
            // Process using the message processor
            var enhancedContent = _messageProcessor.ProcessHl7Message(fileContent);

            // Save enhanced message
            SaveEnhancedMessage(enhancedContent, filePath);

            _statistics.FilesEnhanced++;
            return true;
        }
        catch (Hl7ProcessingException ex)
        {
            _statistics.ErrorsEncountered++;

            // Log error
            LogError(ex, new Dictionary<string, object> { { "stack", ex.StackTrace ?? string.Empty } });
            _logger.LogError(ex, "Error processing {FilePath}: {Message}", filePath, ex.Message);

            // Quarantine file for certain error types
            if (ex.ErrorCode is "HL7_PARSING_FAILURE" or "PROCESSING_ERROR")
            {
                QuarantineFile(filePath, ex);
            }

            return false;
        }
        catch (Exception ex)
        {
            // Unexpected error
            var error = new Hl7ProcessingException($"Unexpected error: {ex.Message}", "PROCESSING_ERROR", ex, filePath);
            _statistics.ErrorsEncountered++;
            LogError(error, new Dictionary<string, object> { { "stack", ex.StackTrace ?? string.Empty } });
            _logger.LogError(ex, "Unexpected error processing {FilePath}", filePath);
            return false;
        }
    }

    /// <summary>
    /// Main processing loop
    /// </summary>
    public void Run()
    {
        var startTime = DateTime.Now;

        _logger.LogInformation("Starting HL7 Message Enhancement Engine");

        try
        {
            // Find all HL7 files
            var hl7Files = FindHl7Files();

            if (hl7Files.Count == 0)
            {
                _logger.LogWarning("No HL7 files found in {SourceDirectory}", _sourceDirectory);
                return;
            }

            _logger.LogInformation("Found {Count} HL7 files to process", hl7Files.Count);

            // Process each file
            foreach (var filePath in hl7Files)
            {
                _statistics.FilesProcessed++;
                var success = ProcessFile(filePath);
                if (_verbose && success)
                {
                    _logger.LogDebug("Successfully processed: {FilePath}", filePath);
                }
            }

            // Final statistics
            var elapsedTime = DateTime.Now - startTime;

            _logger.LogInformation("\nPROCESSING COMPLETE");
            _logger.LogInformation("Files processed successfully: {FilesProcessed}", _statistics.FilesProcessed);
            _logger.LogInformation("Files enhanced: {FilesEnhanced}", _statistics.FilesEnhanced);
            _logger.LogInformation("Errors encountered: {ErrorsEncountered}", _statistics.ErrorsEncountered);
            _logger.LogInformation("Files quarantined: {FilesQuarantined}", _statistics.FilesQuarantined);
            _logger.LogInformation("Processing time: {ElapsedTime:F2} seconds", elapsedTime.TotalSeconds);

            if (_statistics.ErrorsEncountered > 0)
            {
                _logger.LogInformation("Check error_details.log for detailed error analysis");
            }
        }
        catch (Hl7ProcessingException ex)
        {
            _logger.LogError(ex, "Processing failed: {Message}", ex.Message);
            LogError(ex);
            throw;
        }
        catch (Exception ex)
        {
            var error = new Hl7ProcessingException($"Unexpected system error: {ex.Message}", "PROCESSING_ERROR", ex, null);
            _logger.LogError(ex, "System error");
            LogError(error, new Dictionary<string, object> { { "stack", ex.StackTrace ?? string.Empty } });
            throw error;
        }
    }
}
