namespace Hl7MessageEnhancer.Interfaces;

using NHapi.Base.Model;
using Hl7MessageEnhancer.Models;

/// <summary>
/// Interface for HL7 message processing operations
/// </summary>
public interface IHl7Processor
{
    /// <summary>
    /// Process a single HL7 message from string content
    /// </summary>
    /// <param name="hl7Content">Raw HL7 message content</param>
    /// <returns>Enhanced HL7 message as string</returns>
    string ProcessHl7Message(string hl7Content);
    
    /// <summary>
    /// Process a single HL7 message and return the NHapi message object
    /// </summary>
    /// <param name="hl7Content">Raw HL7 message content</param>
    /// <returns>Enhanced HL7 message as IMessage</returns>
    IMessage ProcessHl7MessageToObject(string hl7Content);
    
    /// <summary>
    /// Process a file containing HL7 message
    /// </summary>
    /// <param name="filePath">Path to the HL7 file</param>
    /// <returns>True if processing was successful</returns>
    bool ProcessFile(string filePath);
    
    /// <summary>
    /// Get current processing statistics
    /// </summary>
    ProcessingStatistics Statistics { get; }
}