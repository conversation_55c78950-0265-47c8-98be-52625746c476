using Microsoft.Extensions.Logging;
using Hl7MessageEnhancer.Services;
using System.Text;
using Xunit.Abstractions;

namespace TestMessageEnhancer;

/// <summary>
/// Improved end-to-end tests with proper file isolation and better testability
/// Follows the user's guidelines for avoiding "Access Denied" errors and improving unit testability
/// </summary>
public class ImprovedEndToEndTests : IDisposable
{
    private readonly ILogger<Hl7MessageProcessor> _logger;
    private readonly string _testDirectory;
    private readonly string _sourceDirectory;
    private readonly string _outputDirectory;
    private readonly ITestOutputHelper _output;

    public ImprovedEndToEndTests(ITestOutputHelper output)
    {
        _output = output;
        var loggerFactory = LoggerFactory.Create(builder => 
            builder.AddConsole().SetMinimumLevel(LogLevel.Warning));
        _logger = loggerFactory.CreateLogger<Hl7MessageProcessor>();
        
        // Create unique temporary directories for each test run
        _testDirectory = Path.Combine(Path.GetTempPath(), "Hl7Tests", Guid.NewGuid().ToString());
        _sourceDirectory = Path.Combine(_testDirectory, "source");
        _outputDirectory = Path.Combine(_testDirectory, "output");
        
        Directory.CreateDirectory(_sourceDirectory);
        Directory.CreateDirectory(_outputDirectory);
    }

    [Fact]
    public void ProcessHL7Message_UsingNHapiObjectBasedEncoding_ShouldPreserveTrailingSeparators()
    {
        // Arrange
        var processor = new Hl7MessageProcessor(_logger);
        var sampleHl7Content = GetSampleHl7Content();
        
        // Act
        var processedContent = processor.ProcessHl7Message(sampleHl7Content);
        
        // Assert
        processedContent.Should().NotBeNullOrEmpty();
        
        // Verify that trailing separators are preserved (key requirement from user)
        var lines = processedContent.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
        
        // Check PD1 segment has proper trailing pipes
        var pd1Line = lines.FirstOrDefault(l => l.StartsWith("PD1|"));
        pd1Line.Should().NotBeNull("PD1 segment should exist");
        pd1Line!.Should().EndWith("|||||", "PD1 segment should have 5 trailing pipes");
        
        // Check PV1 segment has proper field separators
        var pv1Line = lines.FirstOrDefault(l => l.StartsWith("PV1|"));
        pv1Line.Should().NotBeNull("PV1 segment should exist");
        pv1Line!.Should().Contain("DR NBR^~", "PV1 segment should have correct field separators");
        
        // Check PID segment has proper field separators
        var pidLine = lines.FirstOrDefault(l => l.StartsWith("PID|"));
        pidLine.Should().NotBeNull("PID segment should exist");
        pidLine!.Should().Contain("MRN^~", "PID segment should have correct field separators");
    }

    [Fact]
    public void ProcessHL7File_WithIsolatedDirectories_ShouldNotCauseAccessDeniedErrors()
    {
        // Arrange
        var sourceFilePath = Path.Combine(_sourceDirectory, "sample.hl7");
        var expectedOutputPath = Path.Combine(_outputDirectory, "sample.hl7");
        
        File.WriteAllText(sourceFilePath, GetSampleHl7Content(), Encoding.UTF8);
        
        // Act
        var processorLogger = LoggerFactory.Create(builder => 
            builder.AddConsole().SetMinimumLevel(LogLevel.Warning))
            .CreateLogger<Hl7Processor>();
        var fileProcessor = new Hl7Processor(processorLogger, _sourceDirectory, _outputDirectory, false);
        var success = fileProcessor.ProcessFile(sourceFilePath);
        
        // Assert
        success.Should().BeTrue("File processing should succeed");
        File.Exists(expectedOutputPath).Should().BeTrue("Output file should be created");
        
        var outputContent = File.ReadAllText(expectedOutputPath, Encoding.UTF8);
        outputContent.Should().NotBeNullOrEmpty("Output file should have content");
        outputContent.Should().StartWith("MSH|", "Output should be valid HL7");
    }

    [Fact]
    public void ProcessHL7Message_CompareWithExpectedOutput_ShouldMatchExactly()
    {
        // Arrange
        var processor = new Hl7MessageProcessor(_logger);
        var sampleHl7Content = GetSampleHl7ContentWithObx();
        var expectedHl7Content = GetExpectedHl7Content();
        
        // Act
        var processedContent = processor.ProcessHl7Message(sampleHl7Content);
        
        // Compare line by line for better debugging
        var actualLines = SplitHl7Content(processedContent);
        var expectedLines = SplitHl7Content(expectedHl7Content);
        
        actualLines.Count.Should().Be(expectedLines.Count, 
            "Processed and expected should have same number of lines");
        
        for (int i = 0; i < actualLines.Count; i++)
        {
            if (actualLines[i] != expectedLines[i])
            {
                _output.WriteLine($"MISMATCH at line {i + 1}:");
                 _output.WriteLine($"Processed: '{actualLines[i]}'");
                 _output.WriteLine($"Expected:  '{expectedLines[i]}'");
            }
            actualLines[i].Should().Be(expectedLines[i], 
                $"Line {i + 1} should match exactly\nProcessed: {actualLines[i]}\nExpected:  {expectedLines[i]}");
        }
    }

    [Fact]
    public void ProcessHL7Message_WithMultipleFiles_ShouldHandleEachIndependently()
    {
        // Arrange
        var processor = new Hl7MessageProcessor(_logger);
        var testFiles = new[]
        {
            ("sample1.hl7", GetSampleHl7Content()),
            ("sample2.hl7", GetAlternativeHl7Content()),
            ("sample3.hl7", GetMinimalHl7Content())
        };
        
        var results = new List<string>();
        
        // Act
        foreach (var (fileName, content) in testFiles)
        {
            var sourceFile = Path.Combine(_sourceDirectory, fileName);
            File.WriteAllText(sourceFile, content, Encoding.UTF8);
            
            var processedContent = processor.ProcessHl7Message(content);
            results.Add(processedContent);
        }
        
        // Assert
        results.Should().HaveCount(3, "All files should be processed");
        results.Should().OnlyContain(r => !string.IsNullOrEmpty(r), "All results should have content");
        results.Should().OnlyContain(r => r.StartsWith("MSH|"), "All results should be valid HL7");
        
        // Each result should be different (since input was different)
        results[0].Should().NotBe(results[1]);
        results[1].Should().NotBe(results[2]);
    }

    [Fact]
    public void ProcessHL7Message_WithObjectBasedApproach_ShouldPreserveComplexStructures()
    {
        // Arrange
        var processor = new Hl7MessageProcessor(_logger);
        var complexHl7 = GetComplexHl7WithRepeatingFields();
        
        // Act
        var result = processor.ProcessHl7Message(complexHl7);
        
        // Assert
        result.Should().NotBeNullOrEmpty();
        
        // Verify complex structures are preserved
        result.Should().Contain("~", "Repetition separators should be preserved");
        result.Should().Contain("^", "Component separators should be preserved");
        result.Should().Contain("&", "Sub-component separators should be preserved");
        
        // Verify specific fixes are applied
        result.Should().Contain("|2.8|", "MSH version should be updated to 2.8");
        
        var lines = result.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
        var pd1Line = lines.FirstOrDefault(l => l.StartsWith("PD1|"));
        if (pd1Line != null)
        {
            pd1Line.Should().EndWith("|||||", "PD1 should have trailing pipes");
        }
    }

    [Theory]
    [InlineData("MSH|^~\\&|Test|Test|Test|Test|20231019||ADT^A04|123|P|2.5\nPID|1||123||DOE^JOHN")]
    [InlineData("MSH|^~\\&|Another|Test|Test|Test|20231019||ADT^A08|456|P|2.3\nPID|1||456||SMITH^JANE")]
    public void ProcessHL7Message_WithVariousInputs_ShouldHandleCorrectly(string inputHl7)
    {
        // Arrange
        var processor = new Hl7MessageProcessor(_logger);
        
        // Act
        var result = processor.ProcessHl7Message(inputHl7);
        
        // Assert
        result.Should().NotBeNullOrEmpty();
        result.Should().StartWith("MSH|");
        result.Should().Contain("|2.8|", "Version should always be updated to 2.8");
    }

    private List<string> SplitHl7Content(string content)
    {
        return content.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries)
                     .Where(line => !string.IsNullOrWhiteSpace(line))
                     .ToList();
    }

    private string GetSampleHl7Content()
    {
        return "MSH|^~\\&|Millennium|PHCC|RHAPSODY_ADT|PHCC|20231019232812||ADT^A04|Q7116703829T12946591549|P|2.5||||||8859/1\r\n" +
               "EVN|A04|20231019232741|||56269^Bhuvaneswari^Gopika^Raj^^^^^External Id^Personnel^^^External Identifier^\\R\\56269\r\n" +
               "PID|1||HC02287391^^^MRN^MR^RHAPSODY_CON_SYS^^20240405~***********^^^\"MOI\"^\"SSN\"^^^20240212~HC02287391^^^MRN^MRN~625664-I~03050476^^0^0^^^^^PHCC Historical MRN^Passport^^Historical MRN^Passport^^medicom||ABED^AHMED^^^^^official~ÚÈíÏ^ÇÍãÏ^^^^^usual~^ÇÍãÏ^^^^^usual||19930804000000|male|ÚÈíÏ^ÇÍãÏ^^^^^usual|Non National|AL SHAHANIYA^^^^^Qatar^Home^^Al Sheehaniya~^^^^^^Home~<EMAIL>^^^^^^E-mail||+***********^mobile~+***********^mobile^Tel|55008227^Business^Tel|Arabic|Single||*********^^^NH^FIN NBR|||||||0|||Yemeni||Y\r\n" +
               "ROL|1|AD|PP^Primary Care Provider^HL70443|10096519^Wally^Ahmed^Nourelfalah Mahmoud\r\n" +
               "PD1|||Rawdat Al Khail Health Center____|||||\r\n" +
               "PV1|1|Outpatient|ABS Nursing^^^ABS&ABS Abu Baker^^Ambulatory(s)^Main Bldg ABS||||58024^Chahda Chahda^Mahmoud^^^Dr.^^^DOCCNBR^Personnel^^^COMMUNITY DR NBR~58024^Chahda Chahda^Mahmoud^^^Dr.^^^External Id^Personnel^^^External Identifier|||Emergency Medicine||||Patient - Referred Self||||Outpatient|*********00000001^0^^^Visit Id|Self Pay|||||||||||||||||||ABS Abu Baker||Active|||20231019232741\r\n" +
               "PV2||||||||20231019233000||0|||||||||||Yes|Routine Clinical|^^683858\r\n";
    }

    private string GetSampleHl7ContentWithObx()
    {
        return "MSH|^~\\&|Millennium|PHCC|RHAPSODY_ADT|PHCC|20231019232812||ADT^A04|Q7116703829T12946591549|P|2.3||||||8859/1\r\n" +
               "EVN|A04|20231019232741|||56269^Bhuvaneswari^Gopika^Raj^^^^^External Id^Personnel^^^External Identifier^\\R\\56269\r\n" +
               "PID|1||HC02287391^^^MRN^MR^RHAPSODY_CON_SYS~***********^^^\"MOI\"^\"SSN\"^^^20240212~HC02287391^^^MRN^MRN^~625664-I^03050476^^0^0^^^^^PHCC Historical MRN^Passport^^Historical MRN^Passport^^medicom^^||ABED^AHMED^^^^^official~ÚÈíÏ^ÇÍãÏ^^^^^usual~^ÇÍãÏ^^^^^usual||19930804000000|male|ÚÈíÏ^ÇÍãÏ^^^^^usual|Non National|AL SHAHANIYA^^^^^Qatar^Home^^Al Sheehaniya~^^^^^^Home^^~<EMAIL>^^^^^^E-mail^^||+***********^mobile^~+***********^mobile^Tel|55008227^Business^Tel|Arabic|Single||*********^^^NH^FIN NBR^|||||||0|||Yemeni||No\r\n" +
               "PD1||||||||\r\n" +
               "PV1|1|Outpatient|ABS Nursing^^^ABS&ABS Abu Baker^^Ambulatory(s)^Main Bldg ABS||||58024^Chahda Chahda^Mahmoud^^^Dr.^^^DOCCNBR^Personnel^^^COMMUNITY DR NBR^~58024^Chahda Chahda^Mahmoud^^^Dr.^^^External Id^Personnel^^^External Identifier^|||Emergency Medicine||||Patient - Referred Self||||Outpatient|*********00000001^0^^^Visit Id|Self Pay|||||||||||||||||||ABS Abu Baker||Active|||20231019232741\r\n" +
               "PV2||||||||20231019233000||0|||||||||||Yes|Routine Clinical|^^683858\r\n" +
               "OBX|1|DT|FULLREG||20231019||||||\r\n" +
               "OBX|2|DT|HC EXP DATE||20240405||||||\r\n" +
               "OBX|3|DT|QATAR_ID_EXP||20240212||||||\r\n" +
               "OBX|4|TX|PRIM_ORG_NAME||Rawdat Al Khail Health Center||||||\r\n" +
               "OBX|5|CD|FAMILY_PHYSICIAN||10096519^Wally^Ahmed^Nourelfalah Mahmoud||||||\r\n";
    }

    private string GetExpectedHl7Content()
    {
        return "MSH|^~\\&|Millennium|PHCC|RHAPSODY_ADT|PHCC|20231019232812||ADT^A04|Q7116703829T12946591549|P|2.8||||||8859/1\r\n" +
               "EVN|A04|20231019232741|||56269^Bhuvaneswari^Gopika^Raj^^^^^External Id^Personnel^^^External Identifier^\\R\\56269\r\n" +
               "PID|1||HC02287391^^^MRN^MR^RHAPSODY_CON_SYS^^20240405~***********^^^\"MOI\"^\"SSN\"^^^20240212~HC02287391^^^MRN^MRN^~625664-I^03050476^^0^0^^^^^PHCC Historical MRN^Passport^^Historical MRN^Passport^^medicom^^||ABED^AHMED^^^^^official~ÚÈíÏ^ÇÍãÏ^^^^^usual~^ÇÍãÏ^^^^^usual||19930804000000|male|ÚÈíÏ^ÇÍãÏ^^^^^usual|Non National|AL SHAHANIYA^^^^^Qatar^Home^^Al Sheehaniya~^^^^^^Home~<EMAIL>^^^^^^E-mail||+***********^mobile~+***********^mobile^Tel|55008227^Business^Tel|Arabic|Single||*********^^^NH^FIN NBR|||||||0|||Yemeni||Y\r\n" +
               "ROL|1|AD|PP^Primary Care Provider^HL70443|10096519^Wally^Ahmed^Nourelfalah Mahmoud\r\n" +
               "PD1|||Rawdat Al Khail Health Center____|||||\r\n" +
               "PV1|1|Outpatient|ABS Nursing^^^ABS&ABS Abu Baker^^Ambulatory(s)^Main Bldg ABS||||58024^Chahda Chahda^Mahmoud^^^Dr.^^^DOCCNBR^Personnel^^^COMMUNITY DR NBR^~58024^Chahda Chahda^Mahmoud^^^Dr.^^^External Id^Personnel^^^External Identifier^|||Emergency Medicine||||Patient - Referred Self||||Outpatient|*********00000001^0^^^Visit Id|Self Pay|||||||||||||||||||ABS Abu Baker||Active|||20231019232741\r\n" +
               "PV2||||||||20231019233000||0|||||||||||Yes|Routine Clinical|^^683858\r\n";
    }

    private string GetAlternativeHl7Content()
    {
        return "MSH|^~\\&|TestSystem|TestFacility|TestApp|TestFacility|20231020120000||ADT^A01|TEST123|P|2.5\r\n" +
               "EVN|A01|20231020120000\r\n" +
               "PID|1||TEST123^^^MRN^MR||TEST^PATIENT^^^^^L||19900101|F\r\n" +
               "PV1|1|Inpatient|ICU^^^ICU&ICU^^Inpatient^Main\r\n";
    }

    private string GetMinimalHl7Content()
    {
        return "MSH|^~\\&|Minimal|Test|Test|Test|20231020||ADT^A04|MIN123|P|2.4\r\n" +
               "PID|1||MIN123||MINIMAL^TEST\r\n";
    }

    private string GetComplexHl7WithRepeatingFields()
    {
        return "MSH|^~\\&|Complex|System|Target|System|20231020120000||ADT^A04|COMPLEX123|P|2.5\r\n" +
               "EVN|A04|20231020120000\r\n" +
               "PID|1||COMP123^^^MRN^MR~ALT456^^^ALT^MR||COMPLEX^PATIENT^MIDDLE^JR^^^^^L~ALIAS^NAME^^^^^A||19850615|M||White^Caucasian|123 MAIN ST^^CITY^ST^12345^USA^H~456 ALT ST^^CITY2^ST^67890^USA^B||555-1234^H~555-5678^W|555-9999^B|EN^English|M^Married\r\n" +
               "PD1|||Primary Care Clinic\r\n" +
               "PV1|1|Outpatient|CLINIC^^^CLINIC&CLINIC^^Outpatient^Building A||||DOC123^DOCTOR^ATTENDING^^^MD^^^DOCID^Personnel^^^ATTENDING~DOC456^DOCTOR^CONSULTING^^^MD^^^DOCID2^Personnel^^^CONSULTING|||Internal Medicine||||Walk-in||||Outpatient|VISIT123^0^^^Visit|Insurance|||||||||||||||||||Clinic A||Active|||20231020120000\r\n";
    }

    public void Dispose()
    {
        // Clean up the isolated test directory
        if (Directory.Exists(_testDirectory))
        {
            try
            {
                Directory.Delete(_testDirectory, recursive: true);
            }
            catch (Exception ex)
            {
                _output.WriteLine($"Warning: Could not clean up test directory {_testDirectory}: {ex.Message}");
                // Don't throw - cleanup failures shouldn't fail tests
            }
        }
    }
}