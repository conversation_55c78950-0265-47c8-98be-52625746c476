using Microsoft.Extensions.Logging;
using Hl7MessageEnhancer.Services;
using System.Text;

namespace TestMessageEnhancer;

/// <summary>
/// End-to-end processing tests that validate the complete HL7 message transformation workflow
/// </summary>
public class EndToEndProcessingTests : IDisposable
{
    private readonly ILogger<Hl7Processor> _logger;
    private readonly string _testDirectory;
    private readonly string _sampleFilePath;
    private readonly string _processedFilePath;
    private readonly string _expectedFilePath;

    public EndToEndProcessingTests()
    {
        var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Warning));
        _logger = loggerFactory.CreateLogger<Hl7Processor>();
        
        _testDirectory = Directory.GetCurrentDirectory();
        _sampleFilePath = Path.Combine(_testDirectory, "sample-hl7.hl7");
        _processedFilePath = Path.Combine(_testDirectory, "processed-sample-hl7.hl7");
        _expectedFilePath = Path.Combine(_testDirectory, "expected-hl7.hl7");
    }

    [Fact]
    public void ProcessSampleHL7Message_ShouldProduceExpectedOutput()
    {
        // Arrange
        VerifyTestFilesExist();
        
        // Clean up any existing processed file
        if (File.Exists(_processedFilePath))
        {
            File.Delete(_processedFilePath);
        }

        // Act
        var success = ProcessSampleFile();

        // Assert
        success.Should().BeTrue("Processing should complete successfully");
        File.Exists(_processedFilePath).Should().BeTrue("Processed file should be created");
        
        // Compare with expected output
        var comparisonResult = CompareProcessedWithExpected();
        comparisonResult.IsMatch.Should().BeTrue(comparisonResult.ErrorMessage);
    }

    [Fact]
    public void ProcessSampleHL7Message_WithOverwrite_ShouldReplaceExistingFile()
    {
        // Arrange
        VerifyTestFilesExist();
        
        // Create a dummy processed file first
        File.WriteAllText(_processedFilePath, "DUMMY CONTENT");
        var originalSize = new FileInfo(_processedFilePath).Length;

        // Act
        var success = ProcessSampleFile();

        // Assert
        success.Should().BeTrue("Processing should complete successfully");
        File.Exists(_processedFilePath).Should().BeTrue("Processed file should exist");
        
        var newSize = new FileInfo(_processedFilePath).Length;
        newSize.Should().NotBe(originalSize, "File should be overwritten with new content");
        
        // Verify content is correct
        var comparisonResult = CompareProcessedWithExpected();
        comparisonResult.IsMatch.Should().BeTrue(comparisonResult.ErrorMessage);
    }

    [Fact]
    public void ProcessedMessage_ShouldHaveCorrectTransformations()
    {
        // Arrange
        VerifyTestFilesExist();
        if (File.Exists(_processedFilePath))
        {
            File.Delete(_processedFilePath);
        }

        // Act
        var success = ProcessSampleFile();

        // Assert
        success.Should().BeTrue("Processing should complete successfully");
        
        var processedContent = File.ReadAllText(_processedFilePath);
        var processedLines = SplitHl7Content(processedContent);

        // Verify specific transformations
        VerifyMshTransformation(processedLines);
        VerifyPidTransformation(processedLines);
        VerifyRolSegmentCreation(processedLines);
        VerifyPd1Transformation(processedLines);
        VerifyObxRemoval(processedLines);
    }

    private void VerifyTestFilesExist()
    {
        File.Exists(_sampleFilePath).Should().BeTrue($"Sample file should exist at {_sampleFilePath}");
        File.Exists(_expectedFilePath).Should().BeTrue($"Expected file should exist at {_expectedFilePath}");
    }

    private bool ProcessSampleFile()
    {
        try
        {
            // Create a temporary source directory and copy the sample file
            var tempSourceDir = Path.Combine(_testDirectory, "temp_source");
            var tempOutputDir = Path.Combine(_testDirectory, "temp_output");
            
            // Clean up temp directories
            if (Directory.Exists(tempSourceDir)) Directory.Delete(tempSourceDir, true);
            if (Directory.Exists(tempOutputDir)) Directory.Delete(tempOutputDir, true);
            
            Directory.CreateDirectory(tempSourceDir);
            
            // Copy sample file to temp source
            var tempSamplePath = Path.Combine(tempSourceDir, "sample-hl7.hl7");
            File.Copy(_sampleFilePath, tempSamplePath);

            // Process using Hl7Processor
            var processor = new Hl7Processor(_logger, tempSourceDir, tempOutputDir, false);
            processor.Run();

            // Copy the processed file to the expected location
            var tempProcessedPath = Path.Combine(tempOutputDir, "sample-hl7.hl7");
            if (File.Exists(tempProcessedPath))
            {
                File.Copy(tempProcessedPath, _processedFilePath, true);
            }

            // Clean up temp directories
            if (Directory.Exists(tempSourceDir)) Directory.Delete(tempSourceDir, true);
            if (Directory.Exists(tempOutputDir)) Directory.Delete(tempOutputDir, true);

            return processor.Statistics is { FilesEnhanced: > 0, ErrorsEncountered: 0 };
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to process sample file: {ex.Message}", ex);
        }
    }

    private (bool IsMatch, string ErrorMessage) CompareProcessedWithExpected()
    {
        try
        {
            var processedContent = File.ReadAllText(_processedFilePath, Encoding.UTF8);
            var expectedContent = File.ReadAllText(_expectedFilePath, Encoding.UTF8);

            var processedLines = SplitHl7Content(processedContent);
            var expectedLines = SplitHl7Content(expectedContent);

            if (processedLines.Count != expectedLines.Count)
            {
                return (false, $"Line count mismatch. Processed: {processedLines.Count}, Expected: {expectedLines.Count}");
            }

            for (int i = 0; i < processedLines.Count; i++)
            {
                if (processedLines[i] != expectedLines[i])
                {
                    return (false, $"Line {i + 1} mismatch.\nProcessed: {processedLines[i]}\nExpected:  {expectedLines[i]}");
                }
            }

            return (true, string.Empty);
        }
        catch (Exception ex)
        {
            return (false, $"Error comparing files: {ex.Message}");
        }
    }

    private List<string> SplitHl7Content(string content)
    {
        // Handle different line endings and split into segments
        var normalized = content.Replace("\r\n", "\r").Replace("\n", "\r");
        return normalized.Split('\r', StringSplitOptions.RemoveEmptyEntries).ToList();
    }

    private void VerifyMshTransformation(List<string> processedLines)
    {
        var mshLine = processedLines.FirstOrDefault(l => l.StartsWith("MSH"));
        mshLine.Should().NotBeNull("MSH segment should exist");
        
        var mshFields = mshLine!.Split('|');
        mshFields.Should().HaveCountGreaterThan(12, "MSH should have enough fields");
        mshFields[11].Should().Be("2.8", "MSH-12 (Version ID) should be updated to 2.8 (at array index 11)");
        mshFields[10].Should().Be("P", "MSH-11 (Processing ID) should be set to P (at array index 10)");
    }

    private void VerifyPidTransformation(List<string> processedLines)
    {
        var pidLine = processedLines.FirstOrDefault(l => l.StartsWith("PID"));
        pidLine.Should().NotBeNull("PID segment should exist");
        
        // Verify FULLREG mapping to PID-30 (should be "Y")
        var pidFields = pidLine!.Split('|');
        pidFields.Should().HaveCountGreaterThan(30, "PID should have enough fields for PID-30");
        pidFields[30].Should().Be("Y", "PID-30 should contain 'Y' from FULLREG mapping");
        
        // Verify HC EXP DATE is mapped to PID-3 component
        pidFields[3].Should().Contain("20240405", "PID-3 should contain HC EXP DATE value");
    }

    private void VerifyRolSegmentCreation(List<string> processedLines)
    {
        var rolLine = processedLines.FirstOrDefault(l => l.StartsWith("ROL"));
        rolLine.Should().NotBeNull("ROL segment should be created for FAMILY_PHYSICIAN");
        
        var rolFields = rolLine!.Split('|');
        rolFields[4].Should().Contain("10096519^Wally^Ahmed^Nourelfalah Mahmoud", 
            "ROL-4 should contain the family physician data");
    }

    private void VerifyPd1Transformation(List<string> processedLines)
    {
        var pd1Line = processedLines.FirstOrDefault(l => l.StartsWith("PD1"));
        pd1Line.Should().NotBeNull("PD1 segment should exist");
        
        var pd1Fields = pd1Line!.Split('|');
        pd1Fields.Should().HaveCountGreaterThan(3, "PD1 should have enough fields");
        pd1Fields[3].Should().Be("Rawdat Al Khail Health Center____",
            "PD1-3 should contain the primary organization name with padding");
    }

    private void VerifyObxRemoval(List<string> processedLines)
    {
        var obxLines = processedLines.Where(l => l.StartsWith("OBX")).ToList();
        obxLines.Should().BeEmpty("All OBX segments should be removed after processing");
    }

    [Fact]
    public void ProcessSampleHL7_SaveAsProcessedSampleHL7_ShouldMatchExpectedHL7()
    {
        // Arrange - Verify all required files exist
        File.Exists(_sampleFilePath).Should().BeTrue($"Sample HL7 file must exist at: {_sampleFilePath}");
        File.Exists(_expectedFilePath).Should().BeTrue($"Expected HL7 file must exist at: {_expectedFilePath}");

        // Clean up any existing processed file (overwrite scenario)
        if (File.Exists(_processedFilePath))
        {
            File.Delete(_processedFilePath);
        }

        // Act - Process the sample-hl7.hl7 message
        var processor = CreateProcessorForSingleFile();
        var success = ProcessSingleFileAndSaveAsProcessed();

        // Assert - Verify processing was successful
        success.Should().BeTrue("HL7 message processing should complete successfully");
        File.Exists(_processedFilePath).Should().BeTrue($"Processed file should be created at: {_processedFilePath}");

        // Assert - Compare processed-sample-hl7.hl7 with expected-hl7.hl7
        var filesAreIdentical = CompareFilesLineByLine(_processedFilePath, _expectedFilePath);
        filesAreIdentical.IsIdentical.Should().BeTrue(
            $"Processed file should be identical to expected file.\n{filesAreIdentical.DifferenceDetails}");
    }

    private Hl7Processor CreateProcessorForSingleFile()
    {
        // Create separate temporary directories for source and output
        var tempSourceDir = Path.Combine(_testDirectory, "temp_source");
        var tempOutputDir = Path.Combine(_testDirectory, "temp_output");

        // Clean up any existing temp directories
        if (Directory.Exists(tempSourceDir)) Directory.Delete(tempSourceDir, true);
        if (Directory.Exists(tempOutputDir)) Directory.Delete(tempOutputDir, true);

        // Create the directories
        Directory.CreateDirectory(tempSourceDir);
        Directory.CreateDirectory(tempOutputDir);

        return new Hl7Processor(_logger, tempSourceDir, tempOutputDir, false);
    }

    private bool ProcessSingleFileAndSaveAsProcessed()
    {
        try
        {
            // Read the sample HL7 file
            var sampleContent = File.ReadAllText(_sampleFilePath, Encoding.UTF8);

            // Create separate temporary directories for processing
            var tempSourceDir = Path.Combine(_testDirectory, "temp_source");
            var tempOutputDir = Path.Combine(_testDirectory, "temp_output");

            // Clean up any existing temp directories
            if (Directory.Exists(tempSourceDir)) Directory.Delete(tempSourceDir, true);
            if (Directory.Exists(tempOutputDir)) Directory.Delete(tempOutputDir, true);

            Directory.CreateDirectory(tempSourceDir);
            Directory.CreateDirectory(tempOutputDir);

            var tempInputFile = Path.Combine(tempSourceDir, "sample.hl7");
            File.WriteAllText(tempInputFile, sampleContent, Encoding.UTF8);

            // Process using the processor with separate directories
            var processor = new Hl7Processor(_logger, tempSourceDir, tempOutputDir, false);
            var result = processor.ProcessFile(tempInputFile);

            if (result)
            {
                var tempOutputFile = Path.Combine(tempOutputDir, "sample.hl7");
                if (File.Exists(tempOutputFile))
                {
                    // Copy to the target processed file location
                    File.Copy(tempOutputFile, _processedFilePath, true);
                }
            }

            // Clean up temp directories
            if (Directory.Exists(tempSourceDir)) Directory.Delete(tempSourceDir, true);
            if (Directory.Exists(tempOutputDir)) Directory.Delete(tempOutputDir, true);

            return result && File.Exists(_processedFilePath);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to process and save HL7 file: {ex.Message}", ex);
        }
    }

    private (bool IsIdentical, string DifferenceDetails) CompareFilesLineByLine(string file1Path, string file2Path)
    {
        try
        {
            var file1Content = File.ReadAllText(file1Path, Encoding.UTF8);
            var file2Content = File.ReadAllText(file2Path, Encoding.UTF8);

            var file1Lines = SplitHl7Content(file1Content);
            var file2Lines = SplitHl7Content(file2Content);

            if (file1Lines.Count != file2Lines.Count)
            {
                return (false, $"Line count differs: Processed={file1Lines.Count}, Expected={file2Lines.Count}");
            }

            var differences = new List<string>();
            for (int i = 0; i < file1Lines.Count; i++)
            {
                if (file1Lines[i] != file2Lines[i])
                {
                    differences.Add($"Line {i + 1}:");
                    differences.Add($"  Processed: {file1Lines[i]}");
                    differences.Add($"  Expected:  {file2Lines[i]}");
                }
            }

            if (differences.Any())
            {
                return (false, string.Join(Environment.NewLine, differences));
            }

            return (true, "Files are identical");
        }
        catch (Exception ex)
        {
            return (false, $"Error comparing files: {ex.Message}");
        }
    }

    #region Message Processing Tests for OriginalMessages and EnhancedMessages

    [Fact]
    public void ProcessActualMessageOne_ShouldApplyCorrectTransformations()
    {
        ProcessAndValidateTransformations("actual_message_one.hl7");
    }

    [Fact]
    public void ProcessActualMessageTwo_ShouldApplyCorrectTransformations()
    {
        ProcessAndValidateTransformations("actual_message_two.hl7");
    }

    [Fact]
    public void ProcessActualMessageThree_ShouldApplyCorrectTransformations()
    {
        ProcessAndValidateTransformations("actual_message_three.hl7");
    }

    [Fact]
    public void ProcessActualMessageFour_ShouldApplyCorrectTransformations()
    {
        ProcessAndValidateTransformations("actual_message_four.hl7");
    }

    [Fact]
    public void ProcessActualMessageFive_ShouldApplyCorrectTransformations()
    {
        ProcessAndValidateTransformations("actual_message_five.hl7");
    }

    [Fact]
    public void ProcessActualMessageOne_ShouldMatchExpectedMessageOne()
    {
        ProcessAndCompareMessage("actual_message_one.hl7", "expected_message_one.hl7");
    }

    [Fact]
    public void ProcessActualMessageTwo_ShouldMatchExpectedMessageTwo()
    {
        ProcessAndCompareMessage("actual_message_two.hl7", "expected_message_two.hl7");
    }

    [Fact]
    public void ProcessActualMessageThree_ShouldMatchExpectedMessageThree()
    {
        ProcessAndCompareMessage("actual_message_three.hl7", "expected_message_three.hl7");
    }

    [Fact]
    public void ProcessActualMessageFour_ShouldMatchExpectedMessageFour()
    {
        ProcessAndCompareMessage("actual_message_four.hl7", "expected_message_four.hl7");
    }

    [Fact]
    public void ProcessActualMessageFive_ShouldMatchExpectedMessageFive()
    {
        ProcessAndCompareMessage("actual_message_five.hl7", "expected_message_five.hl7");
    }

    /// <summary>
    /// Process a message and validate that key transformations are applied correctly
    /// </summary>
    /// <param name="originalFileName">Name of the file in OriginalMessages folder</param>
    private void ProcessAndValidateTransformations(string originalFileName)
    {
        // Arrange - Set up file paths
        var projectRoot = GetProjectRootDirectory();
        var originalFilePath = Path.Combine(projectRoot, "OriginalMessages", originalFileName);
        var processedFilePath = Path.Combine(_testDirectory, $"processed_{originalFileName}");

        // Verify input file exists
        File.Exists(originalFilePath).Should().BeTrue($"Original message file should exist at: {originalFilePath}");

        // Clean up any existing processed file
        if (File.Exists(processedFilePath))
        {
            File.Delete(processedFilePath);
        }

        try
        {
            // Act - Process the original message
            var success = ProcessMessageFile(originalFilePath, processedFilePath);

            // Assert - Verify processing was successful
            success.Should().BeTrue($"Processing of {originalFileName} should complete successfully");
            File.Exists(processedFilePath).Should().BeTrue($"Processed file should be created at: {processedFilePath}");

            // Read and validate the processed content
            var processedContent = File.ReadAllText(processedFilePath, Encoding.UTF8);
            var processedLines = SplitHl7Content(processedContent);

            // Validate key transformations
            ValidateKeyTransformations(processedLines, originalFileName);
        }
        finally
        {
            // Clean up processed file
            if (File.Exists(processedFilePath))
            {
                try
                {
                    File.Delete(processedFilePath);
                }
                catch
                {
                    // Ignore cleanup errors
                }
            }
        }
    }

    /// <summary>
    /// Validate that key HL7 transformations have been applied correctly
    /// </summary>
    /// <param name="processedLines">Lines from the processed HL7 message</param>
    /// <param name="originalFileName">Name of the original file for context</param>
    private void ValidateKeyTransformations(List<string> processedLines, string originalFileName)
    {
        // Validate MSH segment transformations
        var mshLine = processedLines.FirstOrDefault(l => l.StartsWith("MSH"));
        mshLine.Should().NotBeNull($"MSH segment should exist in processed {originalFileName}");

        var mshFields = mshLine!.Split('|');
        mshFields.Should().HaveCountGreaterThan(12, "MSH should have enough fields");
        mshFields[11].Should().Be("2.8", "MSH-12 (Version ID) should be updated to 2.8");

        // Validate PID segment exists and has transformations
        var pidLine = processedLines.FirstOrDefault(l => l.StartsWith("PID"));
        pidLine.Should().NotBeNull($"PID segment should exist in processed {originalFileName}");

        var pidFields = pidLine!.Split('|');
        pidFields.Should().HaveCountGreaterThan(30, "PID should have enough fields for transformations");

        // Check if FULLREG mapping was applied (only if FULLREG data exists in an original message)
        // Some messages may not have FULLREG data, so PID-30 might be empty
        if (pidFields.Length > 30 && !string.IsNullOrEmpty(pidFields[30]))
        {
            pidFields[30].Should().Be("Y", "PID-30 should contain 'Y' from FULLREG mapping when FULLREG data exists");
        }

        // Validate ROL segment is created (if FAMILY_PHYSICIAN data exists)
        var rolLine = processedLines.FirstOrDefault(l => l.StartsWith("ROL"));
        if (rolLine != null)
        {
            rolLine.Should().Contain("Primary Care Provider", "ROL segment should contain primary care provider role");
        }

        // Validate that message structure is maintained
        processedLines.Should().NotBeEmpty("Processed message should not be empty");
        processedLines.Count.Should().BeGreaterThan(5, "Processed message should have multiple segments");

        // Validate that essential segments exist
        processedLines.Should().Contain(l => l.StartsWith("MSH"), "MSH segment should be present");
        processedLines.Should().Contain(l => l.StartsWith("PID"), "PID segment should be present");
        processedLines.Should().Contain(l => l.StartsWith("PV1"), "PV1 segment should be present");
    }



    /// <summary>
    /// Generic method to process a message from OriginalMessages and compare with expected output from EnhancedMessages
    /// </summary>
    /// <param name="originalFileName">Name of the file in OriginalMessages folder</param>
    /// <param name="expectedFileName">Name of the file in EnhancedMessages folder</param>
    private void ProcessAndCompareMessage(string originalFileName, string expectedFileName)
    {
        // Arrange - Set up file paths relative to the project root, not the test execution directory
        var projectRoot = GetProjectRootDirectory();
        var originalMessagesDir = Path.Combine(projectRoot, "OriginalMessages");
        var enhancedMessagesDir = Path.Combine(projectRoot, "EnhancedMessages");
        var originalFilePath = Path.Combine(originalMessagesDir, originalFileName);
        var expectedFilePath = Path.Combine(enhancedMessagesDir, expectedFileName);
        var processedFilePath = Path.Combine(_testDirectory, $"processed_{originalFileName}");

        // Verify input files exist
        File.Exists(originalFilePath).Should().BeTrue($"Original message file should exist at: {originalFilePath}");
        File.Exists(expectedFilePath).Should().BeTrue($"Expected message file should exist at: {expectedFilePath}");

        // Clean up any existing processed file
        if (File.Exists(processedFilePath))
        {
            File.Delete(processedFilePath);
        }

        try
        {
            // Act - Process the original message
            var success = ProcessMessageFile(originalFilePath, processedFilePath);

            // Assert - Verify processing was successful
            success.Should().BeTrue($"Processing of {originalFileName} should complete successfully");
            File.Exists(processedFilePath).Should().BeTrue($"Processed file should be created at: {processedFilePath}");

            // Assert - Compare processed output with expected output
            var comparisonResult = CompareFilesLineByLine(processedFilePath, expectedFilePath);
            comparisonResult.IsIdentical.Should().BeTrue(
                $"Processed {originalFileName} should match {expectedFileName}.\n{comparisonResult.DifferenceDetails}");
        }
        finally
        {
            // Clean up a processed file
            if (File.Exists(processedFilePath))
            {
                try
                {
                    File.Delete(processedFilePath);
                }
                catch
                {
                    // Ignore cleanup errors
                }
            }
        }
    }

    /// <summary>
    /// Process a single message file using the Hl7Processor
    /// </summary>
    /// <param name="inputFilePath">Path to the input HL7 file</param>
    /// <param name="outputFilePath">Path where the processed file should be saved</param>
    /// <returns>True if processing was successful</returns>
    private bool ProcessMessageFile(string inputFilePath, string outputFilePath)
    {
        try
        {
            // Read the input HL7 file
            var inputContent = File.ReadAllText(inputFilePath, Encoding.UTF8);

            // Create temporary directories for processing
            var tempSourceDir = Path.Combine(_testDirectory, $"temp_source_{Guid.NewGuid():N}");
            var tempOutputDir = Path.Combine(_testDirectory, $"temp_output_{Guid.NewGuid():N}");

            try
            {
                // Create directories
                Directory.CreateDirectory(tempSourceDir);
                Directory.CreateDirectory(tempOutputDir);

                // Copy input file to temp source directory
                var tempInputFile = Path.Combine(tempSourceDir, Path.GetFileName(inputFilePath));
                File.WriteAllText(tempInputFile, inputContent, Encoding.UTF8);

                // Process using Hl7Processor
                var processor = new Hl7Processor(_logger, tempSourceDir, tempOutputDir, false);
                var result = processor.ProcessFile(tempInputFile);

                if (result)
                {
                    // Copy processed file to the desired output location
                    var tempOutputFile = Path.Combine(tempOutputDir, Path.GetFileName(inputFilePath));
                    if (File.Exists(tempOutputFile))
                    {
                        File.Copy(tempOutputFile, outputFilePath, true);
                    }
                }

                return result && File.Exists(outputFilePath);
            }
            finally
            {
                // Clean up temp directories
                if (Directory.Exists(tempSourceDir)) Directory.Delete(tempSourceDir, true);
                if (Directory.Exists(tempOutputDir)) Directory.Delete(tempOutputDir, true);
            }
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to process message file {inputFilePath}: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Helper method to find the TestMessageEnhancer project directory
    /// </summary>
    /// <returns>Path to the TestMessageEnhancer project directory</returns>
    private string GetProjectRootDirectory()
    {
        var currentDir = Directory.GetCurrentDirectory();
        var directory = new DirectoryInfo(currentDir);

        // Walk up the directory tree to find the TestMessageEnhancer directory
        while (directory != null)
        {
            // Look for the TestMessageEnhancer directory that contains OriginalMessages and EnhancedMessages
            var testMessageEnhancerDir = Path.Combine(directory.FullName, "TestMessageEnhancer");
            if (Directory.Exists(testMessageEnhancerDir) &&
                Directory.Exists(Path.Combine(testMessageEnhancerDir, "OriginalMessages")) &&
                Directory.Exists(Path.Combine(testMessageEnhancerDir, "EnhancedMessages")))
            {
                return testMessageEnhancerDir;
            }

            // If we're already in TestMessageEnhancer directory
            if (directory.Name == "TestMessageEnhancer" &&
                Directory.Exists(Path.Combine(directory.FullName, "OriginalMessages")) &&
                Directory.Exists(Path.Combine(directory.FullName, "EnhancedMessages")))
            {
                return directory.FullName;
            }

            directory = directory.Parent;
        }

        // Fallback: assume we're already in the correct directory
        return currentDir;
    }

    #endregion

    public void Dispose()
    {
        // Clean up any temporary files created during testing
        if (File.Exists(_processedFilePath))
        {
            try
            {
                File.Delete(_processedFilePath);
            }
            catch
            {
                // Ignore cleanup errors
            }
        }
    }
}
