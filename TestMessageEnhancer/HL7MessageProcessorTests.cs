using Microsoft.Extensions.Logging;
using Hl7MessageEnhancer.Services;
using Hl7MessageEnhancer.Exceptions;
using NHapi.Base.Model;
using System.Text;
using Xunit.Abstractions;

namespace TestMessageEnhancer;

/// <summary>
/// Unit tests for the HL7MessageProcessor class
/// Tests focus on the core message processing logic without file I/O dependencies
/// </summary>
public class Hl7MessageProcessorTests : IDisposable
{
    private readonly ILogger<Hl7MessageProcessor> _logger;
    private readonly Hl7MessageProcessor _processor;
    private readonly ITestOutputHelper _output;

    public Hl7MessageProcessorTests(ITestOutputHelper output)
    {
        _output = output;
        var loggerFactory = LoggerFactory.Create(builder => 
            builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        _logger = loggerFactory.CreateLogger<Hl7MessageProcessor>();
        _processor = new Hl7MessageProcessor(_logger);
    }

    [Fact]
    public void ProcessHL7Message_WithValidMessage_ShouldReturnEnhancedMessage()
    {
        // Arrange
        var sampleHl7 = CreateSampleHl7Message();
        
        // Act
        var result = _processor.ProcessHl7Message(sampleHl7);
        
        // Assert
        result.Should().NotBeNullOrEmpty();
        result.Should().StartWith("MSH|");
        result.Should().Contain("2.8"); // Version should be updated to 2.8
    }

    [Fact]
    public void ProcessHL7Message_WithEmptyContent_ShouldThrowException()
    {
        // Arrange
        var emptyContent = "";
        
        // Act & Assert
        var exception = Assert.Throws<Hl7ProcessingException>(() => 
            _processor.ProcessHl7Message(emptyContent));
        exception.ErrorCode.Should().Be("PROCESSING_ERROR");
    }

    [Fact]
    public void ProcessHL7Message_WithNullContent_ShouldThrowException()
    {
        // Arrange
        string? nullContent = null;
        
        // Act & Assert
        var exception = Assert.Throws<Hl7ProcessingException>(() => 
            _processor.ProcessHl7Message(nullContent!));
        exception.ErrorCode.Should().Be("PROCESSING_ERROR");
    }

    [Fact]
    public void ProcessHL7MessageToObject_WithValidMessage_ShouldReturnIMessage()
    {
        // Arrange
        var sampleHl7 = CreateSampleHl7Message();
        
        // Act
        var result = _processor.ProcessHl7MessageToObject(sampleHl7);
        
        // Assert
        result.Should().NotBeNull();
        result.Should().BeAssignableTo<IMessage>();
    }

    [Fact]
    public void ProcessHL7Message_WithMSHSegment_ShouldUpdateVersionTo28()
    {
        // Arrange
        var hl7WithOldVersion = "MSH|^~\\&|Millennium|PHCC|RHAPSODY_ADT|PHCC|20231019232812||ADT^A04|Q7116703829T12946591549|P|2.5\r\n" +
                               "EVN|A04|20231019232741\r\n" +
                               "PID|1||12345^^^MRN^MR||DOE^JOHN^^^^^L||19800101|M\r\n";
        
        // Act
        var result = _processor.ProcessHl7Message(hl7WithOldVersion);
        
        // Assert
        _output.WriteLine($"Actual MSH result: {result}");
        result.Should().Contain("|P|2.8"); // Version should be updated to 2.8
        result.Should().NotContain("|P|2.5"); // Old version should be replaced
    }

    [Fact]
    public void ProcessHL7Message_WithPD1Segment_ShouldAddTrailingPipes()
    {
        // Arrange
        var hl7WithPd1 = "MSH|^~\\&|Millennium|PHCC|RHAPSODY_ADT|PHCC|20231019232812||ADT^A04|Q7116703829T12946591549|P|2.8\r\n" +
                         "EVN|A04|20231019232741\r\n" +
                         "PID|1||12345^^^MRN^MR||DOE^JOHN^^^^^L||19800101|M\r\n" +
                         "PD1|||Rawdat Al Khail Health Center\r\n";
        
        // Act
        var result = _processor.ProcessHl7Message(hl7WithPd1);
        
        // Assert
        _output.WriteLine($"Actual result: {result}");
        result.Should().Contain("PD1|||Rawdat Al Khail Health Center|||||"); // Should have 5 trailing pipes
    }

    [Fact]
    public void ProcessHL7Message_WithPV1Segment_ShouldFixFieldSeparators()
    {
        // Arrange
        var hl7WithPv1Issues = "MSH|^~\\&|Millennium|PHCC|RHAPSODY_ADT|PHCC|20231019232812||ADT^A04|Q7116703829T12946591549|P|2.8\r\n" +
                               "EVN|A04|20231019232741\r\n" +
                               "PID|1||12345^^^MRN^MR||DOE^JOHN^^^^^L||19800101|M\r\n" +
                               "PV1|1|Outpatient|ABS Nursing|||58024^Chahda^Mahmoud^^^Dr.^^^DOCCNBR^Personnel^^^COMMUNITY DR NBR~58024^Chahda^Mahmoud^^^Dr.^^^External Id^Personnel^^^External Identifier|||\r\n";
        
        // Act
        var result = _processor.ProcessHl7Message(hl7WithPv1Issues);
        
        // Assert
        _output.WriteLine($"Actual PV1 result: {result}");
        result.Should().Contain("DR NBR^~"); // Should fix the separator
        result.Should().Contain("External Identifier^|||"); // Should fix the separator
    }

    [Fact]
    public void ProcessHL7Message_WithPIDSegment_ShouldFixFieldSeparators()
    {
        // Arrange
        var hl7WithPidIssues = "MSH|^~\\&|Millennium|PHCC|RHAPSODY_ADT|PHCC|20231019232812||ADT^A04|Q7116703829T12946591549|P|2.8\r\n" +
                               "EVN|A04|20231019232741\r\n" +
                               "PID|1||**********^^^MRN^MR^RHAPSODY_CON_SYS~625664-I^03050476^^0^0^^^^^PHCC Historical MRN~625664-I|||ABED^AHMED^^^^^official||19930804000000|male||Non National||||||Arabic|Single||*********^^^NH^FIN NBR|||||||0|||Yemeni||Y\r\n" +
                               "PV1|1|Outpatient|ABS Nursing\r\n";
        
        // Act
        var result = _processor.ProcessHl7Message(hl7WithPidIssues);
        
        // Assert
        result.Should().Contain("MRN^~625664-I"); // Should fix the separator
    }

    [Fact]
    public void ProcessHL7Message_WithComplexMessage_ShouldPreserveStructure()
    {
        // Arrange
        var complexHl7 = CreateComplexHl7Message();
        
        // Act
        var result = _processor.ProcessHl7Message(complexHl7);
        
        // Assert
        var resultLines = result.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
        resultLines.Should().HaveCountGreaterThan(3); // Should have multiple segments
        resultLines[0].Should().StartWith("MSH|"); // First line should be MSH
        resultLines.Should().Contain(line => line.StartsWith("PID|")); // Should contain PID segment
    }

    [Fact]
    public void Statistics_AfterProcessing_ShouldBeUpdated()
    {
        // Arrange
        var sampleHl7 = CreateSampleHl7Message();
        var initialErrorCount = _processor.Statistics.ErrorsEncountered;
        
        // Act
        _processor.ProcessHl7Message(sampleHl7);
        
        // Assert
        _processor.Statistics.Should().NotBeNull();
        _processor.Statistics.ErrorsEncountered.Should().Be(initialErrorCount); // No new errors
    }

    [Fact]
    public void ProcessHL7Message_WithInvalidHL7_ShouldIncrementErrorCount()
    {
        // Arrange
        var invalidHl7 = "INVALID HL7 CONTENT";
        var initialErrorCount = _processor.Statistics.ErrorsEncountered;
        
        // Act & Assert
        Assert.Throws<Hl7ProcessingException>(() => _processor.ProcessHl7Message(invalidHl7));
        _processor.Statistics.ErrorsEncountered.Should().Be(initialErrorCount + 1);
    }

    [Theory]
    [InlineData("DEFAULT")]
    [InlineData("CUSTOM")]
    public void Constructor_WithDifferentConfigurations_ShouldLoadCorrectly(string configName)
    {
        // Arrange & Act
        var processor = new Hl7MessageProcessor(_logger, configName);
        
        // Assert
        processor.Should().NotBeNull();
        processor.Statistics.Should().NotBeNull();
    }

    private string CreateSampleHl7Message()
    {
        return "MSH|^~\\&|Millennium|PHCC|RHAPSODY_ADT|PHCC|20231019232812||ADT^A04|Q7116703829T12946591549|P|2.3||||||8859/1\r\n" +
               "EVN|A04|20231019232741|||56269^Bhuvaneswari^Gopika^Raj^^^^^External Id^Personnel^^^External Identifier^\\R\\56269\r\n" +
               "PID|1||**********^^^MRN^MR^RHAPSODY_CON_SYS~***********^^^\"MOI\"^\"SSN\"^^^20240212~**********^^^MRN^MRN^~625664-I^03050476^^0^0^^^^^PHCC Historical MRN^Passport^^Historical MRN^Passport^^medicom^^||ABED^AHMED^^^^^official||19930804000000|male||Non National|AL SHAHANIYA^^^^^Qatar^Home^^Al Sheehaniya||+***********^mobile^|55008227^Business^Tel|Arabic|Single||*********^^^NH^FIN NBR^|||||||0|||Yemeni||No\r\n";
    }

    private string CreateComplexHl7Message()
    {
        return "MSH|^~\\&|Millennium|PHCC|RHAPSODY_ADT|PHCC|20231019232812||ADT^A04|Q7116703829T12946591549|P|2.3||||||8859/1\r\n" +
               "EVN|A04|20231019232741|||56269^Bhuvaneswari^Gopika^Raj^^^^^External Id^Personnel^^^External Identifier^\\R\\56269\r\n" +
               "PID|1||**********^^^MRN^MR^RHAPSODY_CON_SYS~***********^^^\"MOI\"^\"SSN\"^^^20240212~**********^^^MRN^MRN^~625664-I^03050476^^0^0^^^^^PHCC Historical MRN^Passport^^Historical MRN^Passport^^medicom^^||ABED^AHMED^^^^^official||19930804000000|male||Non National|AL SHAHANIYA^^^^^Qatar^Home^^Al Sheehaniya||+***********^mobile^|55008227^Business^Tel|Arabic|Single||*********^^^NH^FIN NBR^|||||||0|||Yemeni||No\r\n" +
               "PD1||||||||\r\n" +
               "PV1|1|Outpatient|ABS Nursing^^^ABS&ABS Abu Baker^^Ambulatory(s)^Main Bldg ABS||||58024^Chahda Chahda^Mahmoud^^^Dr.^^^DOCCNBR^Personnel^^^COMMUNITY DR NBR^~58024^Chahda Chahda^Mahmoud^^^Dr.^^^External Id^Personnel^^^External Identifier^|||Emergency Medicine||||Patient - Referred Self||||Outpatient|*********00000001^0^^^Visit Id|Self Pay|||||||||||||||||||ABS Abu Baker||Active|||20231019232741\r\n" +
               "PV2||||||||20231019233000||0|||||||||||Yes|Routine Clinical|^^683858\r\n" +
               "OBX|1|DT|FULLREG||20231019||||||\r\n" +
               "OBX|2|DT|HC EXP DATE||20240405||||||\r\n";
    }

    public void Dispose()
    {
        // Clean up resources if needed
    }
}