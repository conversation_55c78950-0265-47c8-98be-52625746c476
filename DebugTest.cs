using System;
using System.IO;
using System.Text;
using Microsoft.Extensions.Logging;
using Hl7MessageEnhancer.Services;

class Program
{
    static void Main()
    {
        try
        {
            Console.WriteLine("=== Debug Test for HL7 Processing ===");
            
            var testDirectory = Directory.GetCurrentDirectory();
            var sampleFilePath = Path.Combine(testDirectory, "TestMessageEnhancer", "sample-hl7.hl7");
            var expectedFilePath = Path.Combine(testDirectory, "TestMessageEnhancer", "expected-hl7.hl7");
            var processedFilePath = Path.Combine(testDirectory, "TestMessageEnhancer", "debug-processed.hl7");
            
            Console.WriteLine($"Sample file: {sampleFilePath}");
            Console.WriteLine($"Expected file: {expectedFilePath}");
            Console.WriteLine($"Processed file: {processedFilePath}");
            
            // Check if files exist
            Console.WriteLine($"Sample exists: {File.Exists(sampleFilePath)}");
            Console.WriteLine($"Expected exists: {File.Exists(expectedFilePath)}");
            
            if (!File.Exists(sampleFilePath) || !File.Exists(expectedFilePath))
            {
                Console.WriteLine("Required files not found!");
                return;
            }
            
            // Clean up any existing processed file
            if (File.Exists(processedFilePath))
            {
                File.Delete(processedFilePath);
            }
            
            // Read sample content
            var sampleContent = File.ReadAllText(sampleFilePath, Encoding.UTF8);
            Console.WriteLine($"Sample content length: {sampleContent.Length}");
            
            // Create temporary directories for processing
            var tempSourceDir = Path.Combine(testDirectory, "debug_source");
            var tempOutputDir = Path.Combine(testDirectory, "debug_output");
            
            if (Directory.Exists(tempSourceDir))
                Directory.Delete(tempSourceDir, true);
            if (Directory.Exists(tempOutputDir))
                Directory.Delete(tempOutputDir, true);
            
            Directory.CreateDirectory(tempSourceDir);
            
            // Write sample to temp source file
            var tempInputFile = Path.Combine(tempSourceDir, "sample.hl7");
            File.WriteAllText(tempInputFile, sampleContent, Encoding.UTF8);
            
            // Create logger
            using var loggerFactory = LoggerFactory.Create(builder =>
                builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
            var logger = loggerFactory.CreateLogger<Hl7Processor>();
            
            // Create processor and run it
            var processor = new Hl7Processor(logger, tempSourceDir, tempOutputDir, true);
            processor.Run();
            
            // Check if processing was successful
            var success = processor.Statistics.FilesEnhanced > 0 && processor.Statistics.ErrorsEncountered == 0;
            Console.WriteLine($"Processing success: {success}");
            Console.WriteLine($"Files enhanced: {processor.Statistics.FilesEnhanced}");
            Console.WriteLine($"Errors: {processor.Statistics.ErrorsEncountered}");
            
            if (success)
            {
                // Copy processed file to target location
                var tempOutputFile = Path.Combine(tempOutputDir, "sample.hl7");
                if (File.Exists(tempOutputFile))
                {
                    var processedContent = File.ReadAllText(tempOutputFile, Encoding.UTF8);
                    File.WriteAllText(processedFilePath, processedContent, Encoding.UTF8);
                    Console.WriteLine($"Processed file created: {processedFilePath}");
                    
                    // Compare with expected
                    var expectedContent = File.ReadAllText(expectedFilePath, Encoding.UTF8);
                    
                    Console.WriteLine($"Processed content length: {processedContent.Length}");
                    Console.WriteLine($"Expected content length: {expectedContent.Length}");
                    
                    // Compare line by line
                    var processedLines = processedContent.Replace("\r\n", "\r").Replace("\n", "\r").Split('\r', StringSplitOptions.RemoveEmptyEntries);
                    var expectedLines = expectedContent.Replace("\r\n", "\r").Replace("\n", "\r").Split('\r', StringSplitOptions.RemoveEmptyEntries);
                    
                    Console.WriteLine($"Processed lines: {processedLines.Length}");
                    Console.WriteLine($"Expected lines: {expectedLines.Length}");
                    
                    for (int i = 0; i < Math.Max(processedLines.Length, expectedLines.Length); i++)
                    {
                        var processedLine = i < processedLines.Length ? processedLines[i] : "<MISSING>";
                        var expectedLine = i < expectedLines.Length ? expectedLines[i] : "<MISSING>";
                        
                        if (processedLine != expectedLine)
                        {
                            Console.WriteLine($"DIFFERENCE at line {i + 1}:");
                            Console.WriteLine($"  Processed: {processedLine}");
                            Console.WriteLine($"  Expected:  {expectedLine}");
                            
                            // Show character-by-character comparison for this line
                            var maxLen = Math.Max(processedLine.Length, expectedLine.Length);
                            for (int j = 0; j < maxLen; j++)
                            {
                                var pChar = j < processedLine.Length ? processedLine[j] : '?';
                                var eChar = j < expectedLine.Length ? expectedLine[j] : '?';
                                if (pChar != eChar)
                                {
                                    Console.WriteLine($"    Char {j}: Processed='{pChar}'({(int)pChar}) Expected='{eChar}'({(int)eChar})");
                                }
                            }
                        }
                        else
                        {
                            Console.WriteLine($"Line {i + 1}: MATCH");
                        }
                    }
                }
                else
                {
                    Console.WriteLine("Temp output file not found!");
                }
            }
            
            // Clean up temp directories
            if (Directory.Exists(tempSourceDir)) Directory.Delete(tempSourceDir, true);
            if (Directory.Exists(tempOutputDir)) Directory.Delete(tempOutputDir, true);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }
}
