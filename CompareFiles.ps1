# PowerShell script to compare the processed and expected HL7 files
$processedFile = "C:\Users\<USER>\RiderProjects\Hl7MessageEnhancer\TestDebugOutput\sample-hl7.hl7"
$expectedFile = "C:\Users\<USER>\RiderProjects\Hl7MessageEnhancer\TestOutput2\expected-hl7.hl7"

$processedContent = Get-Content $processedFile -Raw
$expectedContent = Get-Content $expectedFile -Raw

Write-Host "=== PROCESSED CONTENT ==="
Write-Host $processedContent
Write-Host ""

Write-Host "=== EXPECTED CONTENT ==="
Write-Host $expectedContent
Write-Host ""

# Split by segments (using carriage return)
$processedSegments = $processedContent -split '\r' | Where-Object { $_ -ne '' }
$expectedSegments = $expectedContent -split '\r' | Where-Object { $_ -ne '' }

Write-Host "=== SEGMENT COMPARISON ==="
Write-Host "Processed segments: $($processedSegments.Count)"
Write-Host "Expected segments: $($expectedSegments.Count)"
Write-Host ""

for ($i = 0; $i -lt [Math]::Max($processedSegments.Count, $expectedSegments.Count); $i++) {
    $processedSeg = if ($i -lt $processedSegments.Count) { $processedSegments[$i] } else { "[MISSING]" }
    $expectedSeg = if ($i -lt $expectedSegments.Count) { $expectedSegments[$i] } else { "[MISSING]" }
    
    if ($processedSeg -ne $expectedSeg) {
        Write-Host "DIFFERENCE at segment $($i + 1):"
        Write-Host "Processed: $processedSeg"
        Write-Host "Expected:  $expectedSeg"
        Write-Host ""
        
        # Find first character difference
        $minLength = [Math]::Min($processedSeg.Length, $expectedSeg.Length)
        for ($j = 0; $j -lt $minLength; $j++) {
            if ($processedSeg[$j] -ne $expectedSeg[$j]) {
                $procChar = $processedSeg[$j]
                $expChar = $expectedSeg[$j]
                Write-Host "First difference at position $j - '$procChar' vs '$expChar'"
                break
            }
        }
        if ($processedSeg.Length -ne $expectedSeg.Length) {
            Write-Host "Length difference: Processed=$($processedSeg.Length), Expected=$($expectedSeg.Length)"
        }
        Write-Host "---"
    } else {
        Write-Host "Segment $($i + 1): MATCH"
    }
}