﻿<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=8404f7e1_002D2f6a_002D4d44_002Dabc7_002Dbdc47a0aba38/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" IsActive="True" Name="All tests from &amp;lt;TestMessageEnhancer&amp;gt;" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;
  &lt;Or&gt;
    &lt;ProjectFile&gt;06506433-07B0-4181-91A9-40E598EE587F/f:SimpleEndToEndTest.cs&lt;/ProjectFile&gt;
    &lt;ProjectFile&gt;06506433-07B0-4181-91A9-40E598EE587F/f:EndToEndProcessingTests.cs&lt;/ProjectFile&gt;
  &lt;/Or&gt;
&lt;/SessionState&gt;</s:String></wpf:ResourceDictionary>