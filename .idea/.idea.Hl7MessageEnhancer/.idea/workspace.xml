<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile>Hl7MessageEnhancer/Hl7MessageEnhancer.csproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="328836a3-4e4e-4a7b-b493-eeb6455cb477" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/.idea.Hl7MessageEnhancer/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/.idea.Hl7MessageEnhancer/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Hl7MessageEnhancer.sln.DotSettings.user" beforeDir="false" afterPath="$PROJECT_DIR$/Hl7MessageEnhancer.sln.DotSettings.user" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Hl7MessageEnhancer/Interfaces/IHL7Processor.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Hl7MessageEnhancer/Interfaces/IHL7Processor.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Hl7MessageEnhancer/Services/HL7MessageProcessor.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Hl7MessageEnhancer/Services/HL7MessageProcessor.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Hl7MessageEnhancer/Services/HL7Processor.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Hl7MessageEnhancer/Services/HL7Processor.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/TestMessageEnhancer/EndToEndProcessingTests.cs" beforeDir="false" afterPath="$PROJECT_DIR$/TestMessageEnhancer/EndToEndProcessingTests.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/TestMessageEnhancer/HL7MessageProcessorTests.cs" beforeDir="false" afterPath="$PROJECT_DIR$/TestMessageEnhancer/HL7MessageProcessorTests.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/TestMessageEnhancer/ImprovedEndToEndTests.cs" beforeDir="false" afterPath="$PROJECT_DIR$/TestMessageEnhancer/ImprovedEndToEndTests.cs" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;nirzaf&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/nirzaf/Hl7MessageEnhancer.git&quot;,
    &quot;accountId&quot;: &quot;9b5884cd-c155-4fee-ba20-87b8b32cc541&quot;
  }
}</component>
  <component name="HighlightingSettingsPerFile">
    <setting file="mock:///Users/<USER>/RiderProjects/Hl7MessageEnhancer/TestMessageEnhancer/EndToEndProcessingTests.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="mock:///Users/<USER>/RiderProjects/Hl7MessageEnhancer/TestMessageEnhancer/EndToEndProcessingTests.cs" root0="SKIP_HIGHLIGHTING" />
  </component>
  <component name="MetaFilesCheckinStateConfiguration" checkMetaFiles="true" />
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2zZry8OMoa7C2iMsPlyteVd2wuN" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;gem-rules&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected=".NET Project.Hl7MessageEnhancer">
    <configuration name="Hl7MessageEnhancer" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/Hl7MessageEnhancer/Hl7MessageEnhancer.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="328836a3-4e4e-4a7b-b493-eeb6455cb477" name="Changes" comment="" />
      <created>1751949689453</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751949689453</updated>
      <workItem from="1751949696535" duration="7412000" />
      <workItem from="1752001613517" duration="224000" />
      <workItem from="1752001863903" duration="1674000" />
    </task>
    <task id="LOCAL-00001" summary="feat(HL7MessageProcessor): add enhanced message test files for ADT^A04 processing&#10;&#10;- Introduce expected and actual test files for enhanced ADT^A04 messages&#10;- Include new test cases with expanded segments (PID, PV1, PV2, ROL, and OBX)&#10;- Ensure compatibility with HL7 v2.8 message schemas">
      <option name="closed" value="true" />
      <created>1752002561369</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752002561369</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityCheckinConfiguration" checkUnsavedScenes="true" />
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
    <MESSAGE value="feat(HL7MessageProcessor): add enhanced message test files for ADT^A04 processing&#10;&#10;- Introduce expected and actual test files for enhanced ADT^A04 messages&#10;- Include new test cases with expanded segments (PID, PV1, PV2, ROL, and OBX)&#10;- Ensure compatibility with HL7 v2.8 message schemas" />
    <option name="LAST_COMMIT_MESSAGE" value="feat(HL7MessageProcessor): add enhanced message test files for ADT^A04 processing&#10;&#10;- Introduce expected and actual test files for enhanced ADT^A04 messages&#10;- Include new test cases with expanded segments (PID, PV1, PV2, ROL, and OBX)&#10;- Ensure compatibility with HL7 v2.8 message schemas" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="3" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>